# API Authentication - MbokaTour

## Vue d'ensemble
Ce document décrit l'implémentation de l'authentification dans l'application MbokaTour.

## Architecture d'authentification

### Services principaux
- **AuthService** : Service singleton pour gérer l'état d'authentification
- **AuthRepository** : Interface avec l'API pour les opérations d'authentification
- **AuthController** : Logique métier pour les écrans d'authentification

### Flux d'authentification

#### 1. Connexion
```dart
// Endpoint: POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Réponse:**
```json
{
  "success": true,
  "token": "bearer_token_here",
  "user": {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>"
  }
}
```

#### 2. Inscription
```dart
// Endpoint: POST /api/auth/register
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password",
  "password_confirmation": "password"
}
```

#### 3. Récupération du profil utilisateur
```dart
// Endpoint: GET /api/auth/user
// Headers: Authorization: Bearer {token}
```

#### 4. Déconnexion
```dart
// Endpoint: POST /api/auth/logout
// Headers: Authorization: Bearer {token}
```

### Stockage sécurisé
- **SharedPreferences** pour stocker le token et les données utilisateur
- Clés de stockage :
  - `auth_token` : Token d'authentification
  - `user_data` : Données utilisateur sérialisées

### Widgets d'authentification

#### AuthGuard
Protège les routes qui nécessitent une authentification :
```dart
AuthGuard(
  child: ProtectedScreen(),
  fallback: OnboardingScreen(),
)
```

#### AuthRequiredActionButton
Bouton qui vérifie automatiquement l'authentification :
```dart
AuthRequiredActionButton(
  onPressed: () => performAction(),
  child: Text('Action'),
)
```

#### SimpleAuthDialog
Dialog pour demander la connexion :
```dart
context.showAuthDialog(
  title: 'Connexion requise',
  message: 'Veuillez vous connecter pour continuer',
)
```

### Gestion des erreurs
- **401 Unauthorized** : Token expiré ou invalide
- **422 Validation Error** : Données d'entrée invalides
- **500 Server Error** : Erreur serveur

### Configuration API
```dart
// Production
const String baseUrl = 'https://mbokatour.com/api';

// Développement
const String baseUrl = 'http://localhost:8000/api';
```

### Endpoints authentifiés
Les endpoints suivants nécessitent un token Bearer :
- `/api/user/favorites/*`
- `/api/places/likes/*`
- `/api/places/{id}/comments` (POST)
- `/api/auth/user`
- `/api/auth/logout`

### Utilisation dans l'application

#### Vérification de l'état d'authentification
```dart
final authService = AuthService.instance;
if (authService.isAuthenticated) {
  // Utilisateur connecté
  final user = authService.currentUser;
  final token = authService.currentToken;
}
```

#### Initialisation au démarrage
```dart
await AuthService.instance.initialize();
```

#### Actions nécessitant une authentification
- Ajouter aux favoris
- Liker un lieu
- Commenter un lieu
- Accéder au profil utilisateur

### Sécurité
- Les tokens sont stockés de manière sécurisée
- Validation automatique des tokens
- Gestion de l'expiration des sessions
- Protection contre les attaques CSRF avec les tokens Bearer
