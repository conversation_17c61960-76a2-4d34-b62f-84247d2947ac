// Exemple d'utilisation du SearchStylePlaceCard dans l'écran des favoris

import 'package:flutter/material.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/widgets/search_style_place_card.dart';
import 'package:mbokatour/controllers/place_controller.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  List<Place> _favoritePlaces = [];
  bool _isLoading = true;
  late PlaceController _placeController;

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    try {
      final favorites = await _placeController.getFavoritePlaces();
      setState(() {
        _favoritePlaces = favorites;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _removeFavorite(String placeId) async {
    try {
      await _placeController.removeFromFavorite(placeId);
      setState(() {
        _favoritePlaces.removeWhere((place) => place.id == placeId);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Lieu retiré des favoris'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _toggleLike(String placeId) async {
    try {
      final place = _favoritePlaces.firstWhere((p) => p.id == placeId);
      if (place.isLiked == true) {
        await _placeController.removeLike(placeId);
      } else {
        await _placeController.addLike(placeId);
      }

      // Recharger la liste pour mettre à jour l'état
      _loadFavorites();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mes Favoris'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _favoritePlaces.isEmpty
              ? _buildEmptyState()
              : RefreshIndicator(
                  onRefresh: _loadFavorites,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _favoritePlaces.length,
                    itemBuilder: (context, index) {
                      final place = _favoritePlaces[index];
                      return SearchStylePlaceCard(
                        place: place,
                        isFavorite: true, // Toujours true dans les favoris
                        isLiked: place.isLiked ?? false,
                        showActions: true, // Afficher les boutons d'action
                        onFavorite: () => _removeFavorite(place.id),
                        onLike: () => _toggleLike(place.id),
                        margin: const EdgeInsets.only(bottom: 12),
                      );
                    },
                  ),
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun favori pour le moment',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Explorez des lieux et ajoutez-les à vos favoris !',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              // Naviguer vers l'écran de découverte
              Navigator.pop(context);
            },
            child: const Text('Découvrir des lieux'),
          ),
        ],
      ),
    );
  }
}

// Exemple d'utilisation dans SearchScreen
class SearchScreenExample extends StatelessWidget {
  final List<Place> searchResults;

  const SearchScreenExample({
    super.key,
    required this.searchResults,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: searchResults.length,
      itemBuilder: (context, index) {
        final place = searchResults[index];
        return SearchStylePlaceCard(
          place: place,
          // Pas d'actions dans la recherche, juste navigation
          showActions: false,
          margin: const EdgeInsets.only(bottom: 12),
        );
      },
    );
  }
}

// Exemple d'utilisation avec vidéos et distance
class NearbyPlacesExample extends StatelessWidget {
  final List<Place> nearbyPlaces;
  final Map<String, String> distances;

  const NearbyPlacesExample({
    super.key,
    required this.nearbyPlaces,
    required this.distances,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: nearbyPlaces.length,
      itemBuilder: (context, index) {
        final place = nearbyPlaces[index];
        return SearchStylePlaceCard(
          place: place,
          distance: distances[place.id], // Afficher la distance
          showActions: true,
          isFavorite: place.isFavorited ?? false,
          isLiked: place.isLiked ?? false,
          onFavorite: () {
            // Logique pour ajouter/retirer des favoris
          },
          onLike: () {
            // Logique pour liker/unliker
          },
          margin: const EdgeInsets.only(bottom: 12),
        );
      },
    );
  }
}

// Exemple d'utilisation dans les recommandations (comme actuellement)
class RecommendationsExample extends StatelessWidget {
  final List<Place> recommendations;

  const RecommendationsExample({
    super.key,
    required this.recommendations,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: recommendations
          .map((place) => SearchStylePlaceCard(
                place: place,
                showActions: false, // Pas d'actions dans les recommandations
                margin: const EdgeInsets.only(bottom: 8),
              ))
          .toList(),
    );
  }
}
