# Configuration Google Maps - MbokaTour

## Vue d'ensemble
Ce document explique la configuration de Google Maps dans l'application MbokaTour.

## Prérequis
1. Compte Google Cloud Platform
2. Projet GCP avec facturation activée
3. APIs Google Maps activées

## APIs requises
Activez ces APIs dans la console Google Cloud :
- **Maps SDK for Android**
- **Maps SDK for iOS**
- **Places API**
- **Geocoding API**
- **Directions API**
- **Geolocation API**

## Configuration Android

### 1. Clé API Android
Ajoutez votre clé API dans `android/app/src/main/AndroidManifest.xml` :

```xml
<application>
    <meta-data
        android:name="com.google.android.geo.API_KEY"
        android:value="VOTRE_CLE_API_ANDROID" />
</application>
```

### 2. Permissions Android
Dans `android/app/src/main/AndroidManifest.xml` :

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

### 3. Configuration Gradle
Dans `android/app/build.gradle` :

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }
}
```

## Configuration iOS

### 1. Clé API iOS
Ajoutez votre clé API dans `ios/Runner/AppDelegate.swift` :

```swift
import UIKit
import Flutter
import GoogleMaps

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("VOTRE_CLE_API_IOS")
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
```

### 2. Permissions iOS
Dans `ios/Runner/Info.plist` :

```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>Cette app utilise la localisation pour vous montrer les lieux à proximité.</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>Cette app utilise la localisation pour vous montrer les lieux à proximité.</string>
```

### 3. Configuration Podfile
Dans `ios/Podfile` :

```ruby
platform :ios, '12.0'

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end
```

## Dépendances Flutter

### pubspec.yaml
```yaml
dependencies:
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  flutter_polyline_points: ^1.0.0
```

## Utilisation dans l'application

### 1. Carte de base
```dart
GoogleMap(
  initialCameraPosition: CameraPosition(
    target: LatLng(-4.4419, 15.2663), // Kinshasa
    zoom: 12.0,
  ),
  onMapCreated: (GoogleMapController controller) {
    _mapController = controller;
  },
  markers: _markers,
  polylines: _polylines,
)
```

### 2. Géolocalisation
```dart
Position position = await Geolocator.getCurrentPosition(
  desiredAccuracy: LocationAccuracy.high,
);
```

### 3. Marqueurs personnalisés
```dart
Marker(
  markerId: MarkerId(place.id),
  position: LatLng(place.latitude, place.longitude),
  icon: await _createCustomMarker(place.category),
  onTap: () => _showPlaceDetails(place),
)
```

### 4. Directions et routes
```dart
PolylinePoints polylinePoints = PolylinePoints();
PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(
  googleApiKey: "VOTRE_CLE_API",
  request: PolylineRequest(
    origin: PointLatLng(startLat, startLng),
    destination: PointLatLng(endLat, endLng),
    mode: TravelMode.driving,
  ),
);
```

## Fonctionnalités implémentées

### 1. Carte des lieux proches (NearbyMapScreen)
- Affichage des lieux dans un rayon défini
- Marqueurs personnalisés par catégorie
- Info windows avec détails des lieux
- Bouton pour étendre la zone de recherche

### 2. Navigation GPS (RouteScreen)
- Calcul d'itinéraire en temps réel
- Affichage de la route sur la carte
- Instructions de navigation
- Estimation du temps de trajet

### 3. Recherche de lieux
- Recherche par nom ou adresse
- Filtrage par catégorie
- Géocodage inverse pour les coordonnées

## Optimisations

### 1. Performance
```dart
// Limitation du nombre de marqueurs
static const int maxMarkersOnMap = 50;

// Clustering pour les zones denses
if (places.length > maxMarkersOnMap) {
  // Implémenter le clustering
}
```

### 2. Cache des tuiles
```dart
GoogleMap(
  liteModeEnabled: false, // Mode complet pour les interactions
  buildingsEnabled: true,
  trafficEnabled: false, // Désactivé pour économiser les données
)
```

### 3. Gestion des erreurs
```dart
try {
  Position position = await Geolocator.getCurrentPosition();
} on LocationServiceDisabledException {
  // Service de localisation désactivé
} on PermissionDeniedException {
  // Permission refusée
}
```

## Sécurité

### 1. Restriction des clés API
Dans la console Google Cloud :
- Restreindre par application (Android/iOS)
- Restreindre par APIs utilisées
- Surveiller l'utilisation

### 2. Variables d'environnement
```dart
// Ne jamais exposer les clés dans le code
const String googleMapsApiKey = String.fromEnvironment('GOOGLE_MAPS_API_KEY');
```

## Limites et quotas

### Quotas gratuits (par jour)
- **Maps SDK** : Illimité
- **Places API** : 1000 requêtes
- **Directions API** : 2500 requêtes
- **Geocoding API** : 2500 requêtes

### Optimisation des coûts
- Cache des résultats de géocodage
- Limitation des requêtes Directions
- Utilisation du mode Lite quand approprié

## Débogage

### 1. Logs Android
```bash
adb logcat | grep -i "google\|maps"
```

### 2. Logs iOS
```bash
# Dans Xcode Console
# Filtrer par "GoogleMaps" ou "GMSServices"
```

### 3. Vérification des clés API
```dart
void _checkApiKey() {
  if (googleMapsApiKey.isEmpty) {
    print('⚠️ Clé API Google Maps manquante');
  }
}
```

## Ressources utiles
- [Documentation Google Maps Flutter](https://pub.dev/packages/google_maps_flutter)
- [Console Google Cloud](https://console.cloud.google.com/)
- [Pricing Google Maps](https://cloud.google.com/maps-platform/pricing)
- [Geolocator Package](https://pub.dev/packages/geolocator)
