class User {
  final int id;
  final String name;
  final String phoneNumber;
  final String? email;
  final String? createdAt;
  final String? updatedAt;

  User({
    required this.id,
    required this.name,
    required this.phoneNumber,
    this.email,
    this.createdAt,
    this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      phoneNumber: json['phone_number'],
      email: json['email'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone_number': phoneNumber,
      'email': email,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

class AuthResponse {
  final String message;
  final User user;
  final String accessToken;
  final String tokenType;

  AuthResponse({
    required this.message,
    required this.user,
    required this.accessToken,
    required this.tokenType,
  });

  factory AuthResponse.from<PERSON>son(Map<String, dynamic> json) {
    return AuthResponse(
      message: json['message'],
      user: User.from<PERSON><PERSON>(json['user']),
      accessToken: json['access_token'],
      tokenType: json['token_type'],
    );
  }
}
