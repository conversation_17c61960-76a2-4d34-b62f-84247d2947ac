class AppVersion {
  final String currentVersion;
  final String minimumVersion;
  final String latestVersion;
  final bool forceUpdate;
  final bool hasUpdate;
  final String? updateMessage;
  final String? downloadUrl;
  final List<String>? features;

  AppVersion({
    required this.currentVersion,
    required this.minimumVersion,
    required this.latestVersion,
    required this.forceUpdate,
    required this.hasUpdate,
    this.updateMessage,
    this.downloadUrl,
    this.features,
  });

  factory AppVersion.fromJson(Map<String, dynamic> json) {
    return AppVersion(
      currentVersion: json['current_version'] ?? '',
      minimumVersion: json['minimum_version'] ?? '',
      latestVersion: json['latest_version'] ?? '',
      forceUpdate: json['force_update'] ?? false,
      hasUpdate: json['has_update'] ?? false,
      updateMessage: json['update_message'],
      downloadUrl: json['download_url'],
      features: json['features'] != null 
          ? List<String>.from(json['features']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_version': currentVersion,
      'minimum_version': minimumVersion,
      'latest_version': latestVersion,
      'force_update': forceUpdate,
      'has_update': hasUpdate,
      'update_message': updateMessage,
      'download_url': downloadUrl,
      'features': features,
    };
  }

  /// Compare deux versions (format: "1.0.0")
  static int compareVersions(String version1, String version2) {
    List<int> v1Parts = version1.split('.').map(int.parse).toList();
    List<int> v2Parts = version2.split('.').map(int.parse).toList();

    // Égaliser la longueur des listes
    while (v1Parts.length < v2Parts.length) v1Parts.add(0);
    while (v2Parts.length < v1Parts.length) v2Parts.add(0);

    for (int i = 0; i < v1Parts.length; i++) {
      if (v1Parts[i] < v2Parts[i]) return -1;
      if (v1Parts[i] > v2Parts[i]) return 1;
    }
    return 0;
  }

  /// Vérifie si une mise à jour est nécessaire
  bool get isUpdateRequired {
    return compareVersions(currentVersion, minimumVersion) < 0;
  }

  /// Vérifie si une mise à jour est disponible
  bool get isUpdateAvailable {
    return compareVersions(currentVersion, latestVersion) < 0;
  }

  @override
  String toString() {
    return 'AppVersion(current: $currentVersion, minimum: $minimumVersion, latest: $latestVersion, forceUpdate: $forceUpdate)';
  }
}
