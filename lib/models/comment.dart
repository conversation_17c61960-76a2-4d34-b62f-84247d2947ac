class Comment {
  final int id;
  final String content;
  final double rating;
  final String userName;
  final String? userAvatar;
  final DateTime createdAt;
  final int placeId;

  Comment({
    required this.id,
    required this.content,
    required this.rating,
    required this.userName,
    this.userAvatar,
    required this.createdAt,
    required this.placeId,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'] ?? 0,
      content: json['content'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      userName:
          json['user']?['name'] ?? json['user_name'] ?? 'Utilisateur anonyme',
      userAvatar: json['user']?['avatar'] ?? json['user_avatar'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : DateTime.now(),
      placeId: json['place_id'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'rating': rating,
      'user_name': userName,
      'user_avatar': userAvatar,
      'created_at': createdAt.toIso8601String(),
      'place_id': placeId,
    };
  }

  // Méthode pour obtenir le temps relatif (ex: "Il y a 2 jours")
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 an' : '$years ans';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 mois' : '$months mois';
    } else if (difference.inDays > 7) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? '1 semaine' : '$weeks semaines';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? '1 jour' : '${difference.inDays} jours';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1
          ? '1 heure'
          : '${difference.inHours} heures';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1
          ? '1 minute'
          : '${difference.inMinutes} minutes';
    } else {
      return 'À l\'instant';
    }
  }

  // Méthode pour obtenir un avatar par défaut basé sur le nom
  String get defaultAvatar {
    if (userAvatar != null && userAvatar!.isNotEmpty) {
      return userAvatar!;
    }

    // Générer un avatar par défaut basé sur le nom
    final firstLetter = userName.isNotEmpty ? userName[0].toUpperCase() : 'U';
    final seed = userName.hashCode.abs() % 100;
    return 'https://ui-avatars.com/api/?name=$firstLetter&background=random&seed=$seed';
  }
}
