import 'package:mbokatour/models/category.dart';
import 'package:mbokatour/models/place_image.dart';

class Place {
  Category? get category =>
      (categories != null && categories!.isNotEmpty) ? categories!.first : null;

  String? get displayImageUrl {
    // Priorité: mainImageUrl, puis première image de la liste, sinon null
    if (mainImageUrl != null && mainImageUrl!.isNotEmpty) {
      return mainImageUrl;
    }
    if (images != null && images!.isNotEmpty) {
      return images!.first.imageUrl;
    }
    return null;
  }

  final String id;
  final String name;
  final String? description;
  final String? location;
  final double? price;
  final bool? isFree;
  final double? latitude;
  final double? longitude;
  final String? mainImageUrl;
  final String? mediaType;
  final String? address;
  final String? neighborhood;
  final String? city;
  final String? openingHours;
  final String? status;
  final bool? isActive;
  final bool? isFeatured;
  final int? viewsCount;
  final int? priority;
  final bool? isLiked;
  final bool? isFavorited;
  final int? likesCount;
  final List<Category>? categories;
  final List<PlaceImage>? images;

  Place({
    required this.id,
    required this.name,
    this.description,
    this.location,
    this.price,
    this.isFree,
    this.latitude,
    this.longitude,
    this.mainImageUrl,
    this.mediaType,
    this.address,
    this.neighborhood,
    this.city,
    this.openingHours,
    this.status,
    this.isActive,
    this.isFeatured,
    this.viewsCount,
    this.priority,
    this.isLiked,
    this.isFavorited,
    this.likesCount,
    this.categories,
    this.images,
  });

  factory Place.fromJson(Map<String, dynamic> json) {
    var categoriesList = json['categories'] as List?;
    List<Category>? categoryObjects =
        categoriesList?.map((i) => Category.fromJson(i)).toList();

    var imagesList = json['images'] as List?;
    List<PlaceImage>? imageObjects =
        imagesList?.map((i) => PlaceImage.fromJson(i)).toList();

    return Place(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      location: json['location'],
      price: _parseDouble(json['price']),
      isFree: json['is_free'] == 1,
      latitude: _parseDouble(json['latitude']),
      longitude: _parseDouble(json['longitude']),
      mainImageUrl: json['main_image_url'],
      mediaType: json['mediaType'],
      address: json['address'],
      neighborhood: json['neighborhood'],
      city: json['city'],
      openingHours: json['opening_hours'],
      status: json['status'],
      isActive: json['is_active'],
      isFeatured: json['is_featured'],
      viewsCount: json['views_count'],
      priority: json['priority'],
      isLiked: json['is_liked'] ?? false,
      isFavorited: json['is_favorited'] ?? false,
      likesCount: json['likes_count'] ?? 0,
      categories: categoryObjects,
      images: imageObjects,
    );
  }

  // Méthode helper pour parser les doubles qui peuvent être des strings ou des numbers
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is num) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }
}
