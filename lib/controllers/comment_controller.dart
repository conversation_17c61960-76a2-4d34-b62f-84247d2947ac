import 'package:mbokatour/models/comment.dart';
import 'package:mbokatour/repositories/comment_repository.dart';

class CommentController {
  final CommentRepository repo;

  CommentController(this.repo);

  Future<List<Comment>> getCommentsByPlaceId(String placeId) =>
      repo.getCommentsByPlaceId(placeId);

  Future<Comment> addComment({
    required String placeId,
    required String content,
    required double rating,
  }) =>
      repo.addComment(
        placeId: placeId,
        content: content,
        rating: rating,
      );

  Future<void> deleteComment(int commentId) => repo.deleteComment(commentId);

  Future<Comment> updateComment({
    required int commentId,
    required String content,
    required double rating,
  }) =>
      repo.updateComment(
        commentId: commentId,
        content: content,
        rating: rating,
      );
}
