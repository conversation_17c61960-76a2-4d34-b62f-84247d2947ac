import 'package:mbokatour/models/user.dart';
import 'package:mbokatour/repositories/auth_repository.dart';

class AuthController {
  final AuthRepository repo;

  AuthController(this.repo);

  Future<AuthResponse> register({
    required String name,
    required String phoneNumber,
    required String password,
    required String passwordConfirmation,
    String? email,
  }) {
    return repo.register(
      name: name,
      phoneNumber: phoneNumber,
      password: password,
      passwordConfirmation: passwordConfirmation,
      email: email,
    );
  }

  Future<AuthResponse> login({
    required String login,
    required String password,
  }) {
    return repo.login(login: login, password: password);
  }

  Future<User> getUser(String token) {
    return repo.getUser(token);
  }

  Future<void> logout(String token) {
    return repo.logout(token);
  }

  Future<void> logoutAll(String token) {
    return repo.logoutAll(token);
  }
}
