import 'package:flutter/foundation.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/repositories/place_repository.dart';

class PlaceController {
  final PlaceRepository repo;

  PlaceController(this.repo);

  Future<List<Place>> getDiscover() => repo.getDiscover();

  Future<Place> findPlaceById(String id) => repo.findPlaceById(id);

  Future<List<Place>> filterByCategory(int categoryId) =>
      repo.filterByCategory(categoryId);

  // Like functionality
  Future<Map<String, dynamic>> toggleLike(String placeId) =>
      repo.toggleLike(placeId);

  Future<Map<String, dynamic>> getLikeStatus(String placeId) =>
      repo.getLikeStatus(placeId);

  Future<void> likePlace(String placeId) => repo.likePlace(placeId);

  // Favorite functionality
  Future<void> addToFavorite(String placeId) => repo.addToFavorite(placeId);

  Future<void> removeFromFavorite(String placeId) =>
      repo.removeFromFavorite(placeId);

  Future<List<Place>> getFavoritePlaces() => repo.getFavoritePlaces();

  Future<List<Place>> getLikedPlaces() => repo.getLikedPlaces();

  Future<List<Place>> getNearbyPlaces(double latitude, double longitude,
          {double radiusInKm = 20.0}) =>
      repo.getNearbyPlaces(latitude, longitude, radiusInKm: radiusInKm);

  // Load place details (views are automatically incremented by the API)
  Future<Place> loadPlaceDetails(String placeId) async {
    try {
      // L'API incrémente automatiquement les vues à chaque appel de findById
      final place = await repo.findPlaceById(placeId);
      debugPrint('📈 Place chargé avec vues incrémentées: ${place.name}');
      return place;
    } catch (e) {
      throw Exception('Failed to load place details: $e');
    }
  }
}
