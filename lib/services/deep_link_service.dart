import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mbokatour/views/detail/place_detail_screen.dart';
import 'package:mbokatour/controllers/place_controller.dart';
import 'package:mbokatour/repositories/place_repository.dart';
import 'package:mbokatour/services/api_service.dart';

class DeepLinkService {
  static const MethodChannel _channel = MethodChannel('mbokatour/deeplink');
  static DeepLinkService? _instance;
  static DeepLinkService get instance => _instance ??= DeepLinkService._();

  DeepLinkService._();

  StreamController<String>? _linkStreamController;
  Stream<String>? _linkStream;

  /// Initialise le service de deep links
  void initialize() {
    _linkStreamController = StreamController<String>.broadcast();
    _linkStream = _linkStreamController!.stream;

    // Écouter les liens entrants
    _channel.setMethodCallHandler(_handleMethodCall);
  }

  /// Gère les appels de méthode depuis la plateforme native
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onDeepLink':
        final String link = call.arguments as String;
        _linkStreamController?.add(link);
        break;
    }
  }

  /// Stream des liens entrants
  Stream<String>? get linkStream => _linkStream;

  /// Traite un lien deep link
  Future<void> handleDeepLink(String link, BuildContext context) async {
    try {
      final uri = Uri.parse(link);

      // Gérer les liens MbokaTour (custom scheme) et universels (https)
      if (uri.scheme == 'mbokatour') {
        // Lien custom scheme: mbokatour://place/123
        switch (uri.host) {
          case 'place':
            await _handlePlaceLink(uri, context);
            break;
          default:
            debugPrint('Host non supporté: ${uri.host}');
        }
      } else if (uri.scheme == 'https' && uri.host == 'mbokatour.com') {
        // Lien universel: https://mbokatour.com/place/123
        await _handleUniversalLink(uri, context);
      } else {
        debugPrint('Schéma non supporté: ${uri.scheme}://${uri.host}');
        return;
      }
    } catch (e) {
      debugPrint('Erreur lors du traitement du deep link: $e');
    }
  }

  /// Gère les liens universels (https://mbokatour.com/place/123)
  Future<void> _handleUniversalLink(Uri uri, BuildContext context) async {
    try {
      // Extraire l'ID de la place depuis l'URL
      // Format: https://mbokatour.com/place/123
      final pathSegments = uri.pathSegments;
      if (pathSegments.length < 2 || pathSegments[0] != 'place') {
        debugPrint('Format d\'URL universel invalide: ${uri.path}');
        return;
      }

      final placeId = pathSegments[1];

      if (placeId.isEmpty) {
        debugPrint('ID de place invalide dans l\'URL universelle: $placeId');
        return;
      }

      // Charger et naviguer vers la place
      await _loadAndNavigateToPlace(placeId, context, 'lien universel');
    } catch (e) {
      debugPrint('Erreur lors du traitement du lien universel: $e');
    }
  }

  /// Gère les liens vers les places (custom scheme)
  Future<void> _handlePlaceLink(Uri uri, BuildContext context) async {
    try {
      // Extraire l'ID de la place depuis l'URL
      // Format: mbokatour://place/123
      final pathSegments = uri.pathSegments;
      if (pathSegments.isEmpty) {
        debugPrint('ID de place manquant dans le lien');
        return;
      }

      final placeId = pathSegments.first;

      if (placeId.isEmpty) {
        debugPrint('ID de place invalide: $placeId');
        return;
      }

      // Charger et naviguer vers la place
      await _loadAndNavigateToPlace(placeId, context, 'lien de partage');
    } catch (e) {
      debugPrint('Erreur lors du traitement du lien custom: $e');
    }
  }

  /// Logique commune pour charger et naviguer vers une place
  Future<void> _loadAndNavigateToPlace(
      String placeId, BuildContext context, String source) async {
    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Charger la place depuis l'API
      final placeController = PlaceController(
        PlaceRepository(ApiService.instance.dio),
      );

      final place = await placeController.findPlaceById(placeId);

      // Fermer l'indicateur de chargement
      if (context.mounted) {
        Navigator.pop(context);

        // Naviguer vers la page de détail de la place
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlaceDetailScreen(place: place),
          ),
        );

        // Afficher un message de confirmation
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.link, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child:
                      Text('Ouverture de "${place.name}" depuis le $source !'),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      // Fermer l'indicateur de chargement en cas d'erreur
      if (context.mounted) {
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                      'Impossible de charger cette place. Vérifiez votre connexion.'),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      debugPrint('Erreur lors du chargement de la place: $e');
    }
  }

  /// Nettoie les ressources
  void dispose() {
    _linkStreamController?.close();
    _linkStreamController = null;
    _linkStream = null;
  }
}
