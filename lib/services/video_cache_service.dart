import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:video_player/video_player.dart';

/// Service de gestion du cache pour les vidéos
/// Optimise le préchargement et la mise en cache des vidéos
class VideoCacheService {
  static final VideoCacheService _instance = VideoCacheService._internal();
  factory VideoCacheService() => _instance;
  VideoCacheService._internal();

  // Cache manager personnalisé pour les vidéos
  static final CacheManager _cacheManager = CacheManager(
    Config(
      'video_cache',
      stalePeriod: const Duration(days: 7), // Cache pendant 7 jours
      maxNrOfCacheObjects: 50, // Maximum 50 vidéos en cache
      repo: JsonCacheInfoRepository(databaseName: 'video_cache'),
      fileService: HttpFileService(),
    ),
  );

  // Map pour suivre les contrôleurs en cours de préchargement
  final Map<String, VideoPlayerController> _preloadingControllers = {};
  final Map<String, bool> _preloadingStatus = {};

  /// Précharge une vidéo en arrière-plan
  Future<void> preloadVideo(String videoUrl, String placeId) async {
    if (_preloadingStatus[placeId] == true) {
      debugPrint('🎬 Vidéo déjà en cours de préchargement: $placeId');
      return;
    }

    try {
      _preloadingStatus[placeId] = true;
      debugPrint('🎬 Début préchargement vidéo: $placeId');

      // Télécharge et met en cache la vidéo
      final file = await _cacheManager.getSingleFile(videoUrl);

      if (file.existsSync()) {
        // Crée un contrôleur pour précharger la vidéo
        final controller = VideoPlayerController.file(file);
        _preloadingControllers[placeId] = controller;

        await controller.initialize();
        debugPrint('✅ Vidéo préchargée avec succès: $placeId');
      }
    } catch (e) {
      debugPrint('❌ Erreur préchargement vidéo $placeId: $e');
    } finally {
      _preloadingStatus[placeId] = false;
    }
  }

  /// Récupère un contrôleur vidéo optimisé (depuis le cache si disponible)
  Future<VideoPlayerController?> getVideoController(
      String videoUrl, String placeId) async {
    try {
      // Vérifie si la vidéo est déjà préchargée
      if (_preloadingControllers.containsKey(placeId)) {
        final controller = _preloadingControllers[placeId]!;
        _preloadingControllers.remove(placeId);
        debugPrint('🎯 Utilisation contrôleur préchargé: $placeId');
        return controller;
      }

      // Vérifie si la vidéo est en cache
      final fileInfo = await _cacheManager.getFileFromCache(videoUrl);
      if (fileInfo != null && fileInfo.file.existsSync()) {
        debugPrint('📁 Vidéo trouvée en cache: $placeId');
        return VideoPlayerController.file(fileInfo.file);
      }

      // Télécharge la vidéo et la met en cache
      debugPrint('🌐 Téléchargement vidéo depuis le réseau: $placeId');
      final file = await _cacheManager.getSingleFile(videoUrl);
      return VideoPlayerController.file(file);
    } catch (e) {
      debugPrint('❌ Erreur récupération contrôleur vidéo $placeId: $e');
      // Fallback vers le contrôleur réseau
      return VideoPlayerController.networkUrl(Uri.parse(videoUrl));
    }
  }

  /// Vérifie si une vidéo est en cache
  Future<bool> isVideoCached(String videoUrl) async {
    try {
      final fileInfo = await _cacheManager.getFileFromCache(videoUrl);
      return fileInfo != null && fileInfo.file.existsSync();
    } catch (e) {
      return false;
    }
  }

  /// Nettoie le cache des vidéos
  Future<void> clearVideoCache() async {
    try {
      await _cacheManager.emptyCache();
      _preloadingControllers.clear();
      _preloadingStatus.clear();
      debugPrint('🗑️ Cache vidéo nettoyé');
    } catch (e) {
      debugPrint('❌ Erreur nettoyage cache: $e');
    }
  }

  /// Dispose un contrôleur préchargé
  void disposePreloadedController(String placeId) {
    final controller = _preloadingControllers.remove(placeId);
    controller?.dispose();
    _preloadingStatus.remove(placeId);
  }

  /// Récupère la taille du cache (approximative)
  Future<int> getCacheSize() async {
    try {
      // Pour une estimation simple, on retourne une valeur approximative
      // basée sur le nombre de contrôleurs actifs
      final activeControllers = _preloadingControllers.length;
      const averageVideoSize = 10 * 1024 * 1024; // 10MB par vidéo en moyenne

      return activeControllers * averageVideoSize;
    } catch (e) {
      debugPrint('❌ Erreur calcul taille cache: $e');
      return 0;
    }
  }

  /// Formate la taille en MB
  String formatCacheSize(int bytes) {
    if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Précharge plusieurs vidéos en batch
  Future<void> preloadVideoBatch(List<Map<String, String>> videos) async {
    final futures = videos
        .map((video) => preloadVideo(video['url']!, video['placeId']!))
        .toList();

    await Future.wait(futures);
    debugPrint('🎬 Batch de ${videos.length} vidéos préchargées');
  }

  /// Dispose tous les contrôleurs
  void disposeAll() {
    for (final controller in _preloadingControllers.values) {
      controller.dispose();
    }
    _preloadingControllers.clear();
    _preloadingStatus.clear();
  }
}
