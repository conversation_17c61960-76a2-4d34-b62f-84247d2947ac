import 'package:shared_preferences/shared_preferences.dart';
import 'package:mbokatour/models/user.dart';
import 'dart:convert';

class AuthService {
  static const String _tokenKey = 'access_token';
  static const String _userKey = 'user_data';
  
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();
  
  String? _currentToken;
  User? _currentUser;
  
  // Getters
  String? get currentToken => _currentToken;
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentToken != null && _currentToken!.isNotEmpty;
  
  // Initialiser le service au démarrage de l'app
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _currentToken = prefs.getString(_tokenKey);
    
    final userJson = prefs.getString(_userKey);
    if (userJson != null) {
      try {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        _currentUser = User.fromJson(userMap);
      } catch (e) {
        // Si erreur de parsing, supprimer les données corrompues
        await clearAuth();
      }
    }
  }
  
  // Sauvegarder les données d'authentification
  Future<void> saveAuth(String token, User user) async {
    final prefs = await SharedPreferences.getInstance();
    
    _currentToken = token;
    _currentUser = user;
    
    await prefs.setString(_tokenKey, token);
    await prefs.setString(_userKey, json.encode(user.toJson()));
  }
  
  // Supprimer les données d'authentification
  Future<void> clearAuth() async {
    final prefs = await SharedPreferences.getInstance();
    
    _currentToken = null;
    _currentUser = null;
    
    await prefs.remove(_tokenKey);
    await prefs.remove(_userKey);
  }
  
  // Mettre à jour les informations utilisateur
  Future<void> updateUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    
    _currentUser = user;
    await prefs.setString(_userKey, json.encode(user.toJson()));
  }
  
  // Vérifier si le token est valide (optionnel - peut être étendu)
  bool isTokenValid() {
    // Ici vous pouvez ajouter une logique pour vérifier l'expiration du token
    // Pour l'instant, on vérifie juste s'il existe
    return isAuthenticated;
  }
  
  // Obtenir le header d'autorisation pour les requêtes API
  Map<String, String> getAuthHeaders() {
    if (!isAuthenticated) return {};
    
    return {
      'Authorization': 'Bearer $_currentToken',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }
}
