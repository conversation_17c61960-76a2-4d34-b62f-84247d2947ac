import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:mbokatour/models/app_version.dart';
import 'package:mbokatour/services/api_service.dart';

class VersionService {
  static VersionService? _instance;
  static VersionService get instance => _instance ??= VersionService._();

  VersionService._();

  /// Obtient les informations de version de l'application actuelle
  Future<String> getCurrentVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      debugPrint('Erreur lors de la récupération de la version: $e');
      return '1.0.0'; // Version par défaut
    }
  }

  /// Vérifie la version via l'API
  Future<AppVersion?> checkVersion() async {
    try {
      final currentVersion = await getCurrentVersion();
      
      final response = await ApiService.instance.dio.get(
        '/app/version',
        queryParameters: {
          'current_version': currentVersion,
          'platform': defaultTargetPlatform.name.toLowerCase(),
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Ajouter la version actuelle aux données reçues
        data['current_version'] = currentVersion;
        
        return AppVersion.fromJson(data);
      }
    } catch (e) {
      debugPrint('Erreur lors de la vérification de version: $e');
      
      // En cas d'erreur, retourner un objet par défaut (pas de mise à jour)
      final currentVersion = await getCurrentVersion();
      return AppVersion(
        currentVersion: currentVersion,
        minimumVersion: currentVersion,
        latestVersion: currentVersion,
        forceUpdate: false,
        hasUpdate: false,
      );
    }
    
    return null;
  }

  /// Vérifie si une mise à jour est disponible
  Future<bool> isUpdateAvailable() async {
    final versionInfo = await checkVersion();
    return versionInfo?.isUpdateAvailable ?? false;
  }

  /// Vérifie si une mise à jour est obligatoire
  Future<bool> isUpdateRequired() async {
    final versionInfo = await checkVersion();
    return versionInfo?.isUpdateRequired ?? false;
  }

  /// Obtient l'URL de téléchargement selon la plateforme
  String getStoreUrl() {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return 'https://play.google.com/store/apps/details?id=com.mbokatour.app';
      case TargetPlatform.iOS:
        return 'https://apps.apple.com/app/mbokatour/id123456789'; // Remplacer par votre ID
      default:
        return 'https://mbokatour.com/download';
    }
  }

  /// Méthode pour tester la vérification de version (mode debug)
  Future<AppVersion> getMockVersionForTesting({
    bool forceUpdate = false,
    bool hasUpdate = false,
  }) async {
    final currentVersion = await getCurrentVersion();
    
    return AppVersion(
      currentVersion: currentVersion,
      minimumVersion: forceUpdate ? '2.0.0' : currentVersion,
      latestVersion: hasUpdate ? '1.1.0' : currentVersion,
      forceUpdate: forceUpdate,
      hasUpdate: hasUpdate,
      updateMessage: forceUpdate 
          ? 'Une mise à jour obligatoire est disponible pour continuer à utiliser MbokaTour.'
          : hasUpdate 
              ? 'Une nouvelle version de MbokaTour est disponible avec de nouvelles fonctionnalités !'
              : null,
      downloadUrl: getStoreUrl(),
      features: hasUpdate ? [
        'Nouvelles destinations à découvrir',
        'Interface améliorée',
        'Corrections de bugs',
      ] : null,
    );
  }
}
