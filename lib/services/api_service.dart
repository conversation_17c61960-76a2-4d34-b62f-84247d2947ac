import 'package:dio/dio.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/services/auth_service.dart';

class ApiService {
  final Dio _dio;
  static ApiService? _instance;

  static ApiService get instance => _instance ??= ApiService._();

  ApiService._()
      : _dio = Dio(
          BaseOptions(
            baseUrl: AppConfig.baseUrl,
            connectTimeout: const Duration(milliseconds: 5000),
            receiveTimeout: const Duration(milliseconds: 3000),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          ),
        ) {
    _setupInterceptors();
  }

  void _setupInterceptors() {
    // Intercepteur pour ajouter automatiquement le token d'authentification
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Ajouter le token Bearer si disponible
          final authService = AuthService.instance;
          if (authService.isAuthenticated) {
            options.headers['Authorization'] =
                'Bearer ${authService.currentToken}';
          }
          handler.next(options);
        },
        onError: (error, handler) {
          // G<PERSON>rer les erreurs d'authentification
          if (error.response?.statusCode == 401) {
            // Token expiré ou invalide, déconnecter l'utilisateur
            AuthService.instance.clearAuth();
          }
          handler.next(error);
        },
      ),
    );

    // Intercepteur de logging (en mode debug seulement)
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
      ),
    );
  }

  Dio get dio => _dio;

  // Méthode pour créer une nouvelle instance (utile pour les tests)
  factory ApiService() => instance;
}
