import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationService {
  static const double _defaultRadius = 20.0; // 20km par défaut

  /// Vérifie et demande les permissions de localisation
  static Future<bool> requestLocationPermission() async {
    // Vérifier d'abord les permissions avec permission_handler
    PermissionStatus permission = await Permission.location.status;
    
    if (permission.isDenied) {
      permission = await Permission.location.request();
    }
    
    if (permission.isPermanentlyDenied) {
      // Ouvrir les paramètres de l'application
      await openAppSettings();
      return false;
    }
    
    // Vérifier aussi avec geolocator
    LocationPermission geoPermission = await Geolocator.checkPermission();
    
    if (geoPermission == LocationPermission.denied) {
      geoPermission = await Geolocator.requestPermission();
    }
    
    return geoPermission == LocationPermission.whileInUse ||
           geoPermission == LocationPermission.always;
  }

  /// Vérifie si le service de localisation est activé
  static Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Obtient la position actuelle de l'utilisateur
  static Future<Position?> getCurrentPosition() async {
    try {
      // Vérifier si le service est activé
      bool serviceEnabled = await isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Le service de localisation est désactivé');
      }

      // Vérifier les permissions
      bool hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        throw Exception('Permission de localisation refusée');
      }

      // Obtenir la position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Mise à jour tous les 10 mètres
        ),
      );

      return position;
    } catch (e) {
      // Erreur lors de l'obtention de la position
      return null;
    }
  }

  /// Calcule la distance entre deux points en kilomètres
  static double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    ) / 1000; // Convertir en kilomètres
  }

  /// Formate la distance pour l'affichage
  static String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      return '${(distanceInKm * 1000).round()}m';
    } else if (distanceInKm < 10) {
      return '${distanceInKm.toStringAsFixed(1)}km';
    } else {
      return '${distanceInKm.round()}km';
    }
  }

  /// Vérifie si un lieu est dans le rayon spécifié
  static bool isWithinRadius(
    double userLatitude,
    double userLongitude,
    double placeLatitude,
    double placeLongitude,
    {double radiusInKm = _defaultRadius}
  ) {
    double distance = calculateDistance(
      userLatitude,
      userLongitude,
      placeLatitude,
      placeLongitude,
    );
    return distance <= radiusInKm;
  }

  /// Obtient la position avec gestion d'erreur simplifiée
  static Future<LocationResult> getLocationWithStatus() async {
    try {
      Position? position = await getCurrentPosition();
      if (position != null) {
        return LocationResult.success(position);
      } else {
        return LocationResult.error('Impossible d\'obtenir la position');
      }
    } catch (e) {
      return LocationResult.error(e.toString());
    }
  }
}

/// Classe pour encapsuler le résultat de la géolocalisation
class LocationResult {
  final Position? position;
  final String? error;
  final bool isSuccess;

  LocationResult._({this.position, this.error, required this.isSuccess});

  factory LocationResult.success(Position position) {
    return LocationResult._(position: position, isSuccess: true);
  }

  factory LocationResult.error(String error) {
    return LocationResult._(error: error, isSuccess: false);
  }
}
