import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/views/detail/place_detail_screen.dart';

class PlaceCard extends StatefulWidget {
  final Place place;
  final String? distance;
  final VoidCallback? onFavorite;
  final VoidCallback? onLike;
  final bool isFavorite;
  final bool isLiked;

  const PlaceCard({
    super.key,
    required this.place,
    this.distance,
    this.onFavorite,
    this.onLike,
    this.isFavorite = false,
    this.isLiked = false,
  });

  @override
  State<PlaceCard> createState() => _PlaceCardState();
}

class _PlaceCardState extends State<PlaceCard> with TickerProviderStateMixin {
  late AnimationController _favoriteController;
  late AnimationController _likeController;
  late Animation<double> _favoriteAnimation;
  late Animation<double> _likeAnimation;

  @override
  void initState() {
    super.initState();
    _favoriteController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _likeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _favoriteAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(parent: _favoriteController, curve: Curves.elasticOut),
    );
    _likeAnimation = Tween<double>(begin: 1.0, end: 1.3).animate(
      CurvedAnimation(parent: _likeController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _favoriteController.dispose();
    _likeController.dispose();
    super.dispose();
  }

  void _handleTap() {
    // Simple tap pour voir les détails
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailScreen(place: widget.place),
      ),
    );
  }

  void _handleDoubleTap() {
    // Double tap pour ajouter aux favoris
    _favoriteController.forward().then((_) {
      _favoriteController.reverse();
    });
    widget.onFavorite?.call();
  }

  void _handleLike() {
    _likeController.forward().then((_) {
      _likeController.reverse();
    });
    widget.onLike?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      onDoubleTap: _handleDoubleTap,
      child: Container(
        margin: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            children: [
              // Image de fond
              _buildBackgroundImage(),

              // Gradient overlay
              _buildGradientOverlay(),

              // Contenu
              _buildContent(),

              // Actions (Like et Favorite)
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackgroundImage() {
    return Positioned.fill(
      child: CachedNetworkImage(
        imageUrl: widget.place.displayImageUrl ?? '',
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: AppConfig.primary[100],
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor:
                  AlwaysStoppedAnimation<Color>(AppConfig.primary[500]!),
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: AppConfig.primary[100],
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_not_supported,
                size: 48,
                color: AppConfig.primary[400],
              ),
              const SizedBox(height: 8),
              Text(
                'Image non disponible',
                style: TextStyle(
                  color: AppConfig.primary[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.7),
            ],
            stops: const [0.5, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Positioned(
      left: 20,
      right: 20,
      bottom: 20,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Nom du lieu
          Text(
            widget.place.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),

          // Description
          if (widget.place.description != null)
            Text(
              widget.place.description!,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          const SizedBox(height: 12),

          // Distance et localisation
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppConfig.primary[400],
                size: 16,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  widget.distance ??
                      widget.place.location ??
                      widget.place.city ??
                      'Localisation inconnue',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Positioned(
      top: 16,
      right: 16,
      child: Column(
        children: [
          // Bouton Favorite
          AnimatedBuilder(
            animation: _favoriteAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _favoriteAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () {
                      _favoriteController.forward().then((_) {
                        _favoriteController.reverse();
                      });
                      widget.onFavorite?.call();
                    },
                    icon: Icon(
                      (widget.isFavorite || widget.place.isFavorited == true)
                          ? Icons.favorite
                          : Icons.favorite_border,
                      color: (widget.isFavorite ||
                              widget.place.isFavorited == true)
                          ? Colors.red
                          : Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 8),

          // Bouton Like
          AnimatedBuilder(
            animation: _likeAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _likeAnimation.value,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _handleLike,
                    icon: Icon(
                      (widget.isLiked || widget.place.isLiked == true)
                          ? Icons.thumb_up
                          : Icons.thumb_up_outlined,
                      color: (widget.isLiked || widget.place.isLiked == true)
                          ? AppConfig.primary[500]
                          : Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
