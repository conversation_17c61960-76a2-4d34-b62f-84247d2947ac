import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:mbokatour/models/app_version.dart';
import 'package:mbokatour/config/app_config.dart';

class UpdateDialog extends StatelessWidget {
  final AppVersion versionInfo;
  final VoidCallback? onLater;

  const UpdateDialog({
    super.key,
    required this.versionInfo,
    this.onLater,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !versionInfo.forceUpdate, // Empêche la fermeture si mise à jour forcée
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        contentPadding: EdgeInsets.zero,
        content: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                MaterialColor(AppConfig.primary[500]!.value, AppConfig.primary)
                    .shade50,
                Colors.white,
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header avec icône
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: MaterialColor(AppConfig.primary[500]!.value, AppConfig.primary),
                        borderRadius: BorderRadius.circular(40),
                        boxShadow: [
                          BoxShadow(
                            color: MaterialColor(AppConfig.primary[500]!.value, AppConfig.primary)
                                .withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.system_update,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      versionInfo.forceUpdate 
                          ? 'Mise à jour obligatoire'
                          : 'Nouvelle version disponible',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Version ${versionInfo.latestVersion}',
                      style: TextStyle(
                        fontSize: 16,
                        color: MaterialColor(AppConfig.primary[500]!.value, AppConfig.primary),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              // Contenu
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: [
                    if (versionInfo.updateMessage != null)
                      Text(
                        versionInfo.updateMessage!,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black54,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),

                    if (versionInfo.features != null && versionInfo.features!.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text(
                        'Nouveautés :',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ...versionInfo.features!.map((feature) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(top: 6, right: 8),
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                color: MaterialColor(AppConfig.primary[500]!.value, AppConfig.primary),
                                borderRadius: BorderRadius.circular(3),
                              ),
                            ),
                            Expanded(
                              child: Text(
                                feature,
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: Colors.black54,
                                  height: 1.3,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Boutons d'action
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Bouton principal - Mettre à jour
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: () => _launchStore(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: MaterialColor(AppConfig.primary[500]!.value, AppConfig.primary),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: const Text(
                          'Mettre à jour',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    // Bouton secondaire - Plus tard (seulement si pas forcé)
                    if (!versionInfo.forceUpdate && onLater != null) ...[
                      const SizedBox(height: 12),
                      SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            onLater?.call();
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.black54,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          child: const Text(
                            'Plus tard',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _launchStore(BuildContext context) async {
    final url = versionInfo.downloadUrl ?? _getDefaultStoreUrl();
    
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        if (context.mounted) {
          _showErrorSnackBar(context, 'Impossible d\'ouvrir le store');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Erreur lors de l\'ouverture du store');
      }
    }
  }

  String _getDefaultStoreUrl() {
    // URL par défaut selon la plateforme
    return 'https://play.google.com/store/apps/details?id=com.mbokatour.app';
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Méthode statique pour afficher le dialogue
  static Future<void> show(
    BuildContext context,
    AppVersion versionInfo, {
    VoidCallback? onLater,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: !versionInfo.forceUpdate,
      builder: (context) => UpdateDialog(
        versionInfo: versionInfo,
        onLater: onLater,
      ),
    );
  }
}
