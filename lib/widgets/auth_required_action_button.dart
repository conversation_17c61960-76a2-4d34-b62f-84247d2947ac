import 'package:flutter/material.dart';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/widgets/auth_required_snackbar.dart';

/// Widget bouton qui vérifie automatiquement l'authentification avant d'exécuter une action
class AuthRequiredActionButton extends StatelessWidget {
  final Widget child;
  final VoidCallback onPressed;
  final VoidCallback? onLoginSuccess;
  final String? authMessage;
  final String? loginButtonLabel;
  final ButtonStyle? style;

  const AuthRequiredActionButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.onLoginSuccess,
    this.authMessage,
    this.loginButtonLabel,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: style,
      onPressed: () {
        if (context.checkAuthAndShowSnackbar(
          isAuthenticated: AuthService.instance.isAuthenticated,
          message: authMessage,
          actionLabel: loginButtonLabel,
          onLoginSuccess: onLoginSuccess,
        )) {
          onPressed();
        }
      },
      child: child,
    );
  }
}

/// Widget IconButton qui vérifie automatiquement l'authentification
class AuthRequiredIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final VoidCallback? onLoginSuccess;
  final String? authMessage;
  final String? loginButtonLabel;
  final Color? color;
  final double? size;

  const AuthRequiredIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.onLoginSuccess,
    this.authMessage,
    this.loginButtonLabel,
    this.color,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(icon, color: color, size: size),
      onPressed: () {
        if (context.checkAuthAndShowSnackbar(
          isAuthenticated: AuthService.instance.isAuthenticated,
          message: authMessage,
          actionLabel: loginButtonLabel,
          onLoginSuccess: onLoginSuccess,
        )) {
          onPressed();
        }
      },
    );
  }
}

/// Widget FloatingActionButton qui vérifie automatiquement l'authentification
class AuthRequiredFloatingActionButton extends StatelessWidget {
  final Widget child;
  final VoidCallback onPressed;
  final VoidCallback? onLoginSuccess;
  final String? authMessage;
  final String? loginButtonLabel;
  final Color? backgroundColor;

  const AuthRequiredFloatingActionButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.onLoginSuccess,
    this.authMessage,
    this.loginButtonLabel,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: backgroundColor,
      onPressed: () {
        if (context.checkAuthAndShowSnackbar(
          isAuthenticated: AuthService.instance.isAuthenticated,
          message: authMessage,
          actionLabel: loginButtonLabel,
          onLoginSuccess: onLoginSuccess,
        )) {
          onPressed();
        }
      },
      child: child,
    );
  }
}

/// Mixin pour ajouter facilement la vérification d'authentification à n'importe quel widget
mixin AuthRequiredMixin<T extends StatefulWidget> on State<T> {
  /// Exécute une action seulement si l'utilisateur est connecté
  void executeIfAuthenticated(
    VoidCallback action, {
    String? message,
    String? actionLabel,
    VoidCallback? onLoginSuccess,
  }) {
    if (context.checkAuthAndShowSnackbar(
      isAuthenticated: AuthService.instance.isAuthenticated,
      message: message,
      actionLabel: actionLabel,
      onLoginSuccess: onLoginSuccess,
    )) {
      action();
    }
  }
}
