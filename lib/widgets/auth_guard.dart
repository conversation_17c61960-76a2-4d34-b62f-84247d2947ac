import 'package:flutter/material.dart';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/views/onboarding/onboarding_screen.dart';

class AuthGuard extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const AuthGuard({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    final authService = AuthService.instance;
    
    if (authService.isAuthenticated) {
      return child;
    } else {
      return fallback ?? const OnboardingScreen();
    }
  }
}

class AuthGuardStateful extends StatefulWidget {
  final Widget child;
  final Widget? fallback;

  const AuthGuardStateful({
    super.key,
    required this.child,
    this.fallback,
  });

  @override
  State<AuthGuardStateful> createState() => _AuthGuardStatefulState();
}

class _AuthGuardStatefulState extends State<AuthGuardStateful> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    await AuthService.instance.initialize();
    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final authService = AuthService.instance;
    
    if (authService.isAuthenticated) {
      return widget.child;
    } else {
      return widget.fallback ?? const OnboardingScreen();
    }
  }
}
