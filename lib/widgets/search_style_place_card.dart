import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/views/detail/place_detail_screen.dart';

class SearchStylePlaceCard extends StatefulWidget {
  final Place place;
  final String? distance;
  final VoidCallback? onFavorite;
  final VoidCallback? onLike;
  final bool isFavorite;
  final bool isLiked;
  final bool showActions;
  final EdgeInsets? margin;
  final VideoPlayerController? videoController;

  const SearchStylePlaceCard({
    super.key,
    required this.place,
    this.distance,
    this.onFavorite,
    this.onLike,
    this.isFavorite = false,
    this.isLiked = false,
    this.showActions = false,
    this.margin,
    this.videoController,
  });

  @override
  State<SearchStylePlaceCard> createState() => _SearchStylePlaceCardState();
}

class _SearchStylePlaceCardState extends State<SearchStylePlaceCard> {
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  bool _videoInitialized = false;
  bool _videoError = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    // Disposer le contrôleur Chewie
    _chewieController?.dispose();

    // Ne disposer le contrôleur vidéo que s'il n'est pas réutilisé
    if (_videoController != null &&
        _videoController != widget.videoController) {
      _videoController!.dispose();
    }
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    if (widget.place.mediaType == 'video' &&
        widget.place.mainImageUrl != null) {
      // Réutiliser le contrôleur existant si disponible
      if (widget.videoController != null &&
          widget.videoController!.value.isInitialized) {
        _videoController = widget.videoController;
        _videoInitialized = true;
        return;
      }

      try {
        _videoController = VideoPlayerController.networkUrl(
          Uri.parse(widget.place.mainImageUrl!),
        );
        await _videoController!.initialize();

        if (mounted) {
          // Créer le contrôleur Chewie
          _chewieController = ChewieController(
            videoPlayerController: _videoController!,
            autoPlay: false,
            looping: true,
            showControls: false, // Pas de contrôles pour les cartes
            aspectRatio: _videoController!.value.aspectRatio,
          );

          setState(() {
            _videoInitialized = true;
            _videoError = false;
          });
        }
      } catch (error) {
        if (mounted) {
          setState(() {
            _videoError = true;
            _videoInitialized = false;
          });
        }
      }
    }
  }

  void _navigateToDetail() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailScreen(
          place: widget.place,
          videoController: _videoController,
        ),
      ),
    );
  }

  String _getPlaceRating() {
    // Générer une note basée sur l'ID pour la cohérence
    final ratings = [3.8, 4.1, 4.3, 4.5, 4.7, 4.2, 3.9, 4.4, 4.6, 4.0];
    final rating = ratings[widget.place.id.hashCode.abs() % ratings.length];
    return rating.toStringAsFixed(1);
  }

  Widget _buildMediaWidget() {
    const double mediaSize = 80.0;

    if (widget.place.mediaType == 'video' &&
        widget.place.mainImageUrl != null) {
      if (_videoError) {
        return _buildImageFallback(mediaSize);
      }

      if (_chewieController != null &&
          _videoInitialized &&
          _videoController!.value.isInitialized) {
        return Stack(
          children: [
            SizedBox(
              width: mediaSize,
              height: mediaSize,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Chewie(controller: _chewieController!),
              ),
            ),
            // Overlay vidéo avec contrôles
            GestureDetector(
              onTap: () {
                if (_videoController!.value.isPlaying) {
                  _videoController!.pause();
                } else {
                  _videoController!.play();
                }
                setState(() {});
              },
              child: Container(
                width: mediaSize,
                height: mediaSize,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      _videoController!.value.isPlaying
                          ? Icons.pause_circle_filled
                          : Icons.play_circle_filled,
                      key: ValueKey(_videoController!.value.isPlaying),
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }

      // Chargement de la vidéo
      return Container(
        width: mediaSize,
        height: mediaSize,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    // Image normale
    return _buildImageWidget(mediaSize);
  }

  Widget _buildImageWidget(double size) {
    return CachedNetworkImage(
      imageUrl: widget.place.mainImageUrl ?? '',
      width: size,
      height: size,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        width: size,
        height: size,
        color: Colors.grey[300],
        child: const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        width: size,
        height: size,
        color: Colors.grey[300],
        child: const Icon(Icons.image_not_supported),
      ),
    );
  }

  Widget _buildImageFallback(double size) {
    // Essayer d'afficher une image de fallback depuis les autres images du lieu
    if (widget.place.images != null && widget.place.images!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: widget.place.images!.first.imageUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        errorWidget: (context, url, error) => Container(
          width: size,
          height: size,
          color: Colors.grey[300],
          child: const Icon(Icons.videocam_off),
        ),
      );
    }

    return Container(
      width: size,
      height: size,
      color: Colors.grey[300],
      child: const Icon(Icons.videocam_off),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: widget.margin ?? const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _navigateToDetail,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Média (image ou vidéo)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildMediaWidget(),
              ),
              const SizedBox(width: 12),

              // Informations du lieu
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Nom du lieu
                    Text(
                      widget.place.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Description
                    if (widget.place.description != null)
                      Text(
                        widget.place.description!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    const SizedBox(height: 4),

                    // Localisation
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            widget.place.location ??
                                widget.place.city ??
                                widget.place.address ??
                                'Localisation inconnue',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        // Distance si fournie
                        if (widget.distance != null) ...[
                          const SizedBox(width: 8),
                          Text(
                            widget.distance!,
                            style: TextStyle(
                              fontSize: 12,
                              color: AppConfig.primary[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),

                    // Prix et note
                    Row(
                      children: [
                        // Prix
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: widget.place.price != null
                                ? Colors.blue.withValues(alpha: 0.1)
                                : Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            widget.place.price != null
                                ? '${widget.place.price} CDF'
                                : 'Gratuit',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: widget.place.price != null
                                  ? Colors.blue
                                  : Colors.green,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),

                        // Note
                        Row(
                          children: [
                            const Icon(Icons.star,
                                color: Colors.amber, size: 14),
                            const SizedBox(width: 2),
                            Text(
                              _getPlaceRating(),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),

                        const Spacer(),

                        // Actions (favoris, like) si activées
                        if (widget.showActions) ...[
                          // Bouton Like
                          InkWell(
                            onTap: widget.onLike,
                            borderRadius: BorderRadius.circular(20),
                            child: Padding(
                              padding: const EdgeInsets.all(4),
                              child: Icon(
                                widget.isLiked
                                    ? Icons.thumb_up
                                    : Icons.thumb_up_outlined,
                                size: 18,
                                color: widget.isLiked
                                    ? AppConfig.primary[500]
                                    : Colors.grey[500],
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),

                          // Bouton Favoris
                          InkWell(
                            onTap: widget.onFavorite,
                            borderRadius: BorderRadius.circular(20),
                            child: Padding(
                              padding: const EdgeInsets.all(4),
                              child: Icon(
                                widget.isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                size: 18,
                                color: widget.isFavorite
                                    ? Colors.red
                                    : Colors.grey[500],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              // Flèche de navigation (si pas d'actions)
              if (!widget.showActions)
                const Icon(Icons.arrow_forward_ios,
                    size: 16, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}
