import 'package:flutter/material.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/views/auth/login_screen.dart';

/// Dialog simple pour gérer l'authentification requise
class SimpleAuthDialog {
  /// Affiche un dialog pour demander à l'utilisateur de se connecter
  static Future<bool> show(
    BuildContext context, {
    String? title,
    String? message,
    VoidCallback? onLoginSuccess,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.login, color: AppConfig.primary[600]),
              const SizedBox(width: 8),
              Text(
                title ?? 'Connexion requise',
                style: TextStyle(
                  color: AppConfig.primary[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: Text(
            message ?? 'Vous devez être connecté pour effectuer cette action.',
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'Annuler',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop(false);
                
                // Naviguer vers la page de login
                final loginResult = await Navigator.of(context).push<bool>(
                  MaterialPageRoute(
                    builder: (context) => const LoginScreen(),
                  ),
                );
                
                // Si la connexion a réussi
                if (loginResult == true && onLoginSuccess != null) {
                  onLoginSuccess();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConfig.primary[500],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Se connecter'),
            ),
          ],
        );
      },
    );
    
    return result ?? false;
  }

  /// Vérifie l'authentification et affiche le dialog si nécessaire
  static Future<bool> checkAuthAndShowDialog(
    BuildContext context, {
    required bool isAuthenticated,
    String? title,
    String? message,
    VoidCallback? onLoginSuccess,
  }) async {
    if (!isAuthenticated) {
      await show(
        context,
        title: title,
        message: message,
        onLoginSuccess: onLoginSuccess,
      );
      // Revérifier après le dialog
      return AuthService.instance.isAuthenticated;
    }
    return true;
  }
}

/// Extension pour faciliter l'utilisation
extension SimpleAuthDialogExtension on BuildContext {
  /// Affiche un dialog d'authentification
  Future<bool> showAuthDialog({
    String? title,
    String? message,
    VoidCallback? onLoginSuccess,
  }) {
    return SimpleAuthDialog.show(
      this,
      title: title,
      message: message,
      onLoginSuccess: onLoginSuccess,
    );
  }

  /// Vérifie l'authentification et affiche le dialog si nécessaire
  Future<bool> checkAuthAndShowDialog({
    required bool isAuthenticated,
    String? title,
    String? message,
    VoidCallback? onLoginSuccess,
  }) {
    return SimpleAuthDialog.checkAuthAndShowDialog(
      this,
      isAuthenticated: isAuthenticated,
      title: title,
      message: message,
      onLoginSuccess: onLoginSuccess,
    );
  }
}

/// Widget bouton avec dialog d'authentification
class AuthDialogButton extends StatelessWidget {
  final Widget child;
  final VoidCallback onPressed;
  final VoidCallback? onLoginSuccess;
  final String? authTitle;
  final String? authMessage;
  final ButtonStyle? style;

  const AuthDialogButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.onLoginSuccess,
    this.authTitle,
    this.authMessage,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: style,
      onPressed: () async {
        if (await context.checkAuthAndShowDialog(
          isAuthenticated: AuthService.instance.isAuthenticated,
          title: authTitle,
          message: authMessage,
          onLoginSuccess: onLoginSuccess,
        )) {
          onPressed();
        }
      },
      child: child,
    );
  }
}

/// Widget IconButton avec dialog d'authentification
class AuthDialogIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final VoidCallback? onLoginSuccess;
  final String? authTitle;
  final String? authMessage;
  final Color? color;
  final double? size;

  const AuthDialogIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.onLoginSuccess,
    this.authTitle,
    this.authMessage,
    this.color,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(icon, color: color, size: size),
      onPressed: () async {
        if (await context.checkAuthAndShowDialog(
          isAuthenticated: AuthService.instance.isAuthenticated,
          title: authTitle,
          message: authMessage,
          onLoginSuccess: onLoginSuccess,
        )) {
          onPressed();
        }
      },
    );
  }
}
