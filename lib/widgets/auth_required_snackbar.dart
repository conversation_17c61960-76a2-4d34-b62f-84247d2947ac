import 'package:flutter/material.dart';
import 'package:mbokatour/views/auth/login_screen.dart';

/// Widget réutilisable pour afficher un SnackBar quand une action nécessite une connexion
class AuthRequiredSnackbar {
  /// Affiche un SnackBar avec un bouton pour se connecter
  static void show(
    BuildContext context, {
    String? message,
    String? actionLabel,
    VoidCallback? onLoginSuccess,
  }) {
    final defaultMessage =
        message ?? 'Vous devez être connecté pour effectuer cette action';
    final defaultActionLabel = actionLabel ?? 'Se connecter';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.login, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                defaultMessage,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: defaultActionLabel,
          textColor: Colors.white,
          onPressed: () => _navigateToLogin(context, onLoginSuccess),
        ),
      ),
    );
  }

  /// Navigue vers la page de login avec gestion du retour
  static void _navigateToLogin(
      BuildContext context, VoidCallback? onLoginSuccess) {
    print('🔄 Navigation vers login...');
    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => const LoginScreen(),
      ),
    )
        .then((result) {
      print('🔄 Retour de login avec résultat: $result');
      // Si la connexion a réussi, exécuter le callback
      if (result == true && onLoginSuccess != null) {
        print('✅ Exécution du callback onLoginSuccess');
        onLoginSuccess();
      } else {
        print('❌ Pas de callback ou connexion échouée');
      }
    });
  }

  /// Méthode pour vérifier si l'utilisateur est connecté et afficher le SnackBar si nécessaire
  static bool checkAuthAndShowSnackbar(
    BuildContext context, {
    required bool isAuthenticated,
    String? message,
    String? actionLabel,
    VoidCallback? onLoginSuccess,
  }) {
    if (!isAuthenticated) {
      show(
        context,
        message: message,
        actionLabel: actionLabel,
        onLoginSuccess: onLoginSuccess,
      );
      return false;
    }
    return true;
  }
}

/// Extension pour faciliter l'utilisation depuis n'importe quel BuildContext
extension AuthRequiredSnackbarExtension on BuildContext {
  /// Affiche un SnackBar de connexion requise
  void showAuthRequiredSnackbar({
    String? message,
    String? actionLabel,
    VoidCallback? onLoginSuccess,
  }) {
    AuthRequiredSnackbar.show(
      this,
      message: message,
      actionLabel: actionLabel,
      onLoginSuccess: onLoginSuccess,
    );
  }

  /// Vérifie l'authentification et affiche le SnackBar si nécessaire
  bool checkAuthAndShowSnackbar({
    required bool isAuthenticated,
    String? message,
    String? actionLabel,
    VoidCallback? onLoginSuccess,
  }) {
    return AuthRequiredSnackbar.checkAuthAndShowSnackbar(
      this,
      isAuthenticated: isAuthenticated,
      message: message,
      actionLabel: actionLabel,
      onLoginSuccess: onLoginSuccess,
    );
  }
}
