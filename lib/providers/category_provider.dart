import 'package:flutter/material.dart';
import 'package:mbokatour/models/category.dart';
import 'package:mbokatour/models/place.dart';

class CategoryProvider extends ChangeNotifier {
  List<Category> _categories = [];
  int _selectedCategoryId = 0; // 0 = Tous
  List<Place> _allPlaces = [];
  List<Place> _filteredPlaces = [];

  // Getters
  List<Category> get categories => _categories;
  int get selectedCategoryId => _selectedCategoryId;
  List<Place> get allPlaces => _allPlaces;
  List<Place> get filteredPlaces => _filteredPlaces;

  List<Category> get allCategories {
    if (_categories.isEmpty) {
      // Catégories par défaut si l'API n'est pas encore chargée
      return [
        Category(id: 0, name: 'Tous'),
        Category(id: 1, name: 'Monuments'),
        Category(id: 2, name: 'Musées'),
        Category(id: 3, name: 'Parcs'),
        Category(id: 4, name: 'Restaurants'),
        Category(id: 5, name: 'Shopping'),
      ];
    }
    return [Category(id: 0, name: 'Tous'), ..._categories];
  }

  // Setters
  void setCategories(List<Category> categories) {
    _categories = categories;
    notifyListeners();
  }

  void setPlaces(List<Place> places) {
    _allPlaces = places;
    _filterPlaces();
  }

  void setSelectedCategory(int categoryId) {
    _selectedCategoryId = categoryId;
    _filterPlaces();
  }

  void _filterPlaces() {
    if (_selectedCategoryId == 0) {
      _filteredPlaces = _allPlaces;
    } else {
      _filteredPlaces = _allPlaces.where((place) {
        if (place.categories == null || place.categories!.isEmpty) {
          return false;
        }
        return place.categories!.any((cat) => cat.id == _selectedCategoryId);
      }).toList();
    }
    notifyListeners();
  }

  // Méthode pour obtenir les lieux filtrés pour la carte (lieux à proximité)
  List<Place> filterNearbyPlaces(List<Place> nearbyPlaces) {
    if (_selectedCategoryId == 0) {
      return nearbyPlaces;
    }
    
    return nearbyPlaces.where((place) {
      if (place.categories == null || place.categories!.isEmpty) {
        return false;
      }
      return place.categories!.any((cat) => cat.id == _selectedCategoryId);
    }).toList();
  }
}
