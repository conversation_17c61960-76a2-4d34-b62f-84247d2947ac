import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:mbokatour/config/app_theme.dart';
import 'package:mbokatour/views/splash/splash_screen.dart';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/services/deep_link_service.dart';
import 'package:mbokatour/providers/category_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialiser AuthService au démarrage
  await AuthService.instance.initialize();

  // Initialiser DeepLinkService
  DeepLinkService.instance.initialize();

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
    _initDeepLinks();
  }

  void _initDeepLinks() {
    // Écouter les deep links entrants
    DeepLinkService.instance.linkStream?.listen((link) {
      final context = _navigatorKey.currentContext;
      if (context != null && mounted) {
        DeepLinkService.instance.handleDeepLink(link, context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CategoryProvider(),
      child: MaterialApp(
        title: 'MbokaTour',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        navigatorKey: _navigatorKey,
        home: const SplashScreen(),
      ),
    );
  }
}
