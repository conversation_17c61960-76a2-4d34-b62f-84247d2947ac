import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/views/onboarding/onboarding_screen.dart';
import 'package:mbokatour/views/home/<USER>';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/services/version_service.dart';
import 'package:mbokatour/widgets/update_dialog.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeIn),
    );

    _controller.forward();

    Timer(
      const Duration(seconds: 3),
      () => _checkVersionAndNavigate(),
    );
  }

  /// Vérifie la version et affiche le dialogue si nécessaire
  Future<void> _checkVersionAndNavigate() async {
    try {
      // Vérifier la version de l'application
      final versionInfo = await VersionService.instance.checkVersion();

      if (versionInfo != null && mounted) {
        // Si une mise à jour est requise (forcée)
        if (versionInfo.isUpdateRequired || versionInfo.forceUpdate) {
          await UpdateDialog.show(
            context,
            versionInfo,
            onLater: null, // Pas de "plus tard" pour les mises à jour forcées
          );
          return; // Ne pas naviguer, rester sur le splash
        }

        // Si une mise à jour est disponible (optionnelle)
        if (versionInfo.isUpdateAvailable || versionInfo.hasUpdate) {
          await UpdateDialog.show(
            context,
            versionInfo,
            onLater: () => _navigateToNextScreen(), // Continuer si "plus tard"
          );
          return; // Le dialogue gère la navigation
        }
      }

      // Aucune mise à jour nécessaire, continuer normalement
      _navigateToNextScreen();
    } catch (e) {
      // En cas d'erreur, continuer normalement
      debugPrint('Erreur lors de la vérification de version: $e');
      _navigateToNextScreen();
    }
  }

  void _navigateToNextScreen() {
    if (!mounted) return;

    final authService = AuthService.instance;

    Widget nextScreen;
    if (authService.isAuthenticated) {
      nextScreen = const HomeScreen();
    } else {
      nextScreen = const OnboardingScreen();
    }

    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => nextScreen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppConfig.primary[500]!,
              AppConfig.primary[400]!,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Center(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Image.asset(
                'assets/logos/logo.png',
                width: 200,
                height: 200,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
