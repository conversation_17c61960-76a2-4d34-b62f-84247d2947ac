import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/models/category.dart';
import 'package:mbokatour/models/place_image.dart';
import 'package:mbokatour/widgets/place_card.dart';
import 'package:mbokatour/services/location_service.dart';
import 'package:mbokatour/controllers/place_controller.dart';
import 'package:mbokatour/repositories/place_repository.dart';
import 'package:mbokatour/services/api_service.dart';

class NearbyMapScreen extends StatefulWidget {
  const NearbyMapScreen({super.key});

  @override
  State<NearbyMapScreen> createState() => _NearbyMapScreenState();
}

class _NearbyMapScreenState extends State<NearbyMapScreen> {
  late PlaceController _placeController;
  List<Place> _nearbyPlaces = [];
  Position? _currentPosition;
  bool _isLoading = true;
  String? _errorMessage;
  final Set<String> _favoritePlaces = {};
  final Set<String> _likedPlaces = {};
  double _currentRadius = 20.0; // Rayon de recherche en km
  final List<double> _radiusOptions = [5.0, 10.0, 20.0, 50.0, 100.0];

  // Variables pour la carte
  late MapController _mapController;
  bool _showMapView = false;

  // Position par défaut (Kinshasa) si la géolocalisation échoue
  static const LatLng _defaultLocation = LatLng(-4.3317, 15.3139);

  @override
  void initState() {
    super.initState();
    _placeController = PlaceController(PlaceRepository(ApiService().dio));
    _mapController = MapController();
    _loadNearbyPlaces();
  }

  Future<void> _loadNearbyPlaces() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Obtenir la position actuelle
      LocationResult locationResult =
          await LocationService.getLocationWithStatus();

      if (!locationResult.isSuccess) {
        setState(() {
          _errorMessage = locationResult.error ?? 'Erreur de géolocalisation';
          _isLoading = false;
        });
        return;
      }

      _currentPosition = locationResult.position;

      // Debug: Afficher la position actuelle
      print(
          'Position actuelle: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}');

      // Récupérer les lieux à proximité
      List<Place> places = await _getNearbyPlacesWithFallback();

      setState(() {
        _nearbyPlaces = places;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du chargement des lieux: $e';
        _isLoading = false;
      });
    }
  }

  Future<List<Place>> _getNearbyPlacesWithFallback() async {
    if (_currentPosition == null) return [];

    try {
      // Essayer d'abord l'API
      print(
          'Appel API avec position: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}, rayon: ${_currentRadius}km');
      List<Place> apiPlaces = await _placeController.getNearbyPlaces(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        radiusInKm: _currentRadius,
      );
      print('API réussie: ${apiPlaces.length} lieux trouvés');
      return apiPlaces;
    } catch (e) {
      // En cas d'échec de l'API, utiliser des données de test filtrées par distance
      print('API échouée: $e');
      print(
          'Utilisation des données de test avec position: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}');

      return [];
    }
  }

  String _getDistanceText(Place place) {
    if (_currentPosition == null ||
        place.latitude == null ||
        place.longitude == null) {
      return 'Distance inconnue';
    }

    double distance = LocationService.calculateDistance(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      place.latitude!,
      place.longitude!,
    );

    return LocationService.formatDistance(distance);
  }

  void _toggleFavorite(String placeId) {
    setState(() {
      if (_favoritePlaces.contains(placeId)) {
        _favoritePlaces.remove(placeId);
      } else {
        _favoritePlaces.add(placeId);
      }
    });

    // Appeler l'API pour ajouter/retirer des favoris
    _placeController.addToFavorite(placeId).catchError((error) {
      // Gérer l'erreur si nécessaire
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la mise à jour des favoris'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  void _toggleLike(String placeId) {
    setState(() {
      if (_likedPlaces.contains(placeId)) {
        _likedPlaces.remove(placeId);
      } else {
        _likedPlaces.add(placeId);
      }
    });

    // Appeler l'API pour liker/unliker
    _placeController.likePlace(placeId).catchError((error) {
      // Gérer l'erreur si nécessaire
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la mise à jour des likes'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  void _expandSearchRadius() {
    setState(() {
      // Trouver l'index du rayon actuel et passer au suivant
      int currentIndex = _radiusOptions.indexOf(_currentRadius);
      if (currentIndex < _radiusOptions.length - 1) {
        _currentRadius = _radiusOptions[currentIndex + 1];
      } else {
        // Si on est déjà au maximum, rester au maximum
        _currentRadius = _radiusOptions.last;
      }
    });

    // Recharger les lieux avec le nouveau rayon
    _loadNearbyPlaces();
  }

  String _getRadiusText() {
    if (_currentRadius < 1) {
      return '${(_currentRadius * 1000).toInt()}m';
    } else {
      return '${_currentRadius.toInt()}km';
    }
  }

  String _getNextRadiusText() {
    int currentIndex = _radiusOptions.indexOf(_currentRadius);
    if (currentIndex < _radiusOptions.length - 1) {
      double nextRadius = _radiusOptions[currentIndex + 1];
      if (nextRadius < 1) {
        return '${(nextRadius * 1000).toInt()}m';
      } else {
        return '${nextRadius.toInt()}km';
      }
    }
    return '${_currentRadius.toInt()}km';
  }

  void _toggleMapView() {
    setState(() {
      _showMapView = !_showMapView;
    });

    // Centrer la carte sur la position actuelle quand on passe en vue carte
    if (_showMapView && _currentPosition != null) {
      _mapController.move(
        LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        15.0, // Niveau de zoom
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    bool canExpandRadius = _currentRadius < _radiusOptions.last;
    bool showFab = !_isLoading && _errorMessage == null && canExpandRadius;

    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Lieux à proximité'),
            Text(
              'Rayon: ${_getRadiusText()}${_currentPosition != null ? ' • ${_currentPosition!.latitude.toStringAsFixed(4)}, ${_currentPosition!.longitude.toStringAsFixed(4)}' : ''}',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.normal,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showMapView ? Icons.list : Icons.map),
            onPressed: _toggleMapView,
            tooltip: _showMapView ? 'Vue liste' : 'Vue carte',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNearbyPlaces,
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: showFab
          ? FloatingActionButton.extended(
              onPressed: _expandSearchRadius,
              icon: const Icon(Icons.zoom_out_map),
              label: Text('Élargir à ${_getNextRadiusText()}'),
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    // Afficher la carte même s'il n'y a pas de lieux
    if (_showMapView) {
      return _buildMapView();
    }

    if (_nearbyPlaces.isEmpty) {
      return _buildEmptyState();
    }

    return _buildPlacesList();
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Recherche des lieux à proximité...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur de localisation',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Une erreur est survenue',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadNearbyPlaces,
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    bool canExpandRadius = _currentRadius < _radiusOptions.last;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_searching,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun lieu à proximité',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Aucun lieu trouvé dans un rayon de ${_getRadiusText()} autour de votre position.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _loadNearbyPlaces,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Actualiser'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12),
                  ),
                ),
                if (canExpandRadius) ...[
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _expandSearchRadius,
                    icon: const Icon(Icons.zoom_out_map),
                    label: Text('Élargir (${_getNextRadiusText()})'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMapView() {
    // Position à afficher sur la carte
    LatLng centerPosition = _currentPosition != null
        ? LatLng(_currentPosition!.latitude, _currentPosition!.longitude)
        : _defaultLocation;

    // Créer les marqueurs
    List<Marker> markers = [];

    // Marqueur pour la position actuelle
    if (_currentPosition != null) {
      markers.add(
        Marker(
          point:
              LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          child: const Icon(
            Icons.my_location,
            color: Colors.blue,
            size: 30,
          ),
        ),
      );
    }

    // Marqueurs pour les lieux trouvés
    for (Place place in _nearbyPlaces) {
      if (place.latitude != null && place.longitude != null) {
        markers.add(
          Marker(
            point: LatLng(place.latitude!, place.longitude!),
            child: GestureDetector(
              onTap: () {
                // Afficher les détails du lieu
                _showPlaceDetails(place);
              },
              child: const Icon(
                Icons.location_on,
                color: Colors.red,
                size: 30,
              ),
            ),
          ),
        );
      }
    }

    return FlutterMap(
      mapController: _mapController,
      options: MapOptions(
        initialCenter: centerPosition,
        initialZoom: 15.0,
        minZoom: 5.0,
        maxZoom: 18.0,
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.example.mboka_tour_app',
        ),
        MarkerLayer(markers: markers),
        // Cercle pour montrer le rayon de recherche
        if (_currentPosition != null)
          CircleLayer(
            circles: [
              CircleMarker(
                point: LatLng(
                    _currentPosition!.latitude, _currentPosition!.longitude),
                radius: _currentRadius * 1000, // Convertir km en mètres
                useRadiusInMeter: true,
                color: Colors.blue.withValues(alpha: 0.1),
                borderColor: Colors.blue,
                borderStrokeWidth: 2,
              ),
            ],
          ),
      ],
    );
  }

  void _showPlaceDetails(Place place) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: PlaceCard(
            place: place,
            distance: _getDistanceText(place),
            isFavorite: _favoritePlaces.contains(place.id),
            isLiked: _likedPlaces.contains(place.id),
            onFavorite: () => _toggleFavorite(place.id),
            onLike: () => _toggleLike(place.id),
          ),
        ),
      ),
    );
  }

  Widget _buildPlacesList() {
    return RefreshIndicator(
      onRefresh: _loadNearbyPlaces,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _nearbyPlaces.length,
        itemBuilder: (context, index) {
          final place = _nearbyPlaces[index];
          return Container(
            height: 400,
            margin: const EdgeInsets.only(bottom: 16),
            child: PlaceCard(
              place: place,
              distance: _getDistanceText(place),
              isFavorite: _favoritePlaces.contains(place.id),
              isLiked: _likedPlaces.contains(place.id),
              onFavorite: () => _toggleFavorite(place.id),
              onLike: () => _toggleLike(place.id),
            ),
          );
        },
      ),
    );
  }
}
