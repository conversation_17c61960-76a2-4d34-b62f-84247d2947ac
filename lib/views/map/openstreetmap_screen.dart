import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/services/location_service.dart';
import 'package:mbokatour/controllers/place_controller.dart';
import 'package:mbokatour/repositories/place_repository.dart';
import 'package:mbokatour/services/api_service.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/views/detail/place_detail_screen.dart';
import 'package:mbokatour/providers/category_provider.dart';

class OpenStreetMapScreen extends StatefulWidget {
  const OpenStreetMapScreen({super.key});

  @override
  State<OpenStreetMapScreen> createState() => _OpenStreetMapScreenState();
}

class _OpenStreetMapScreenState extends State<OpenStreetMapScreen> {
  late PlaceController _placeController;
  final MapController _mapController = MapController();
  Position? _currentPosition;
  List<Place> _nearbyPlaces = [];
  List<Place> _allNearbyPlaces = []; // Stocke tous les lieux sans filtre
  List<Marker> _markers = [];
  bool _isLoading = true;
  String? _errorMessage;
  int _lastSelectedCategoryId = 0; // Pour détecter les changements

  // Position par défaut (Kinshasa)
  static const LatLng _defaultLocation = LatLng(-4.3317, 15.3139);

  @override
  void initState() {
    super.initState();
    _placeController =
        PlaceController(PlaceRepository(ApiService.instance.dio));
    _loadMapData();
  }

  Future<void> _loadMapData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Obtenir la position actuelle
      LocationResult locationResult =
          await LocationService.getLocationWithStatus();

      if (locationResult.isSuccess) {
        _currentPosition = locationResult.position;
      }

      // Récupérer les lieux à proximité
      List<Place> allNearbyPlaces = await _getNearbyPlacesWithFallback();

      if (mounted) {
        // Stocker tous les lieux et filtrer selon la catégorie sélectionnée
        final categoryProvider =
            Provider.of<CategoryProvider>(context, listen: false);
        List<Place> filteredPlaces =
            categoryProvider.filterNearbyPlaces(allNearbyPlaces);

        setState(() {
          _allNearbyPlaces = allNearbyPlaces;
          _nearbyPlaces = filteredPlaces;
          _lastSelectedCategoryId = categoryProvider.selectedCategoryId;
          _isLoading = false;
        });

        // Créer les marqueurs
        await _createMarkers();

        // Centrer la carte sur la position de l'utilisateur ou Kinshasa
        LatLng targetLocation = _currentPosition != null
            ? LatLng(_currentPosition!.latitude, _currentPosition!.longitude)
            : _defaultLocation;

        _mapController.move(targetLocation, 12.0);

        // Afficher un message si aucun lieu n'est trouvé
        if (filteredPlaces.isEmpty && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Aucun lieu trouvé à proximité. Essayez d\'élargir votre zone de recherche.'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du chargement: $e';
        _isLoading = false;
      });
    }
  }

  Future<List<Place>> _getNearbyPlacesWithFallback() async {
    if (_currentPosition != null) {
      // Utiliser l'API avec la position actuelle de l'utilisateur
      return await _placeController.getNearbyPlaces(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        radiusInKm: 20.0,
      );
    } else {
      // Utiliser l'API avec la position par défaut (Kinshasa)
      return await _placeController.getNearbyPlaces(
        _defaultLocation.latitude,
        _defaultLocation.longitude,
        radiusInKm: 20.0,
      );
    }
  }

  Future<void> _createMarkers() async {
    List<Marker> markers = [];

    // Marqueur pour la position de l'utilisateur
    if (_currentPosition != null) {
      markers.add(
        Marker(
          point:
              LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          width: 80,
          height: 80,
          child: GestureDetector(
            onTap: () => _showUserLocationInfo(),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.person_pin_circle,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ),
      );
    }

    // Marqueurs pour les lieux
    for (Place place in _nearbyPlaces) {
      if (place.latitude != null && place.longitude != null) {
        markers.add(
          Marker(
            point: LatLng(place.latitude!, place.longitude!),
            width: 80,
            height: 80,
            child: GestureDetector(
              onTap: () => _onMarkerTapped(place),
              child: Container(
                decoration: BoxDecoration(
                  color: AppConfig.primary[500],
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 3),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.place,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ),
        );
      }
    }

    setState(() {
      _markers = markers;
    });
  }

  void _showUserLocationInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Votre position'),
        content: const Text('Vous êtes ici !'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _onMarkerTapped(Place place) {
    // Afficher les détails du lieu en bas de l'écran
    _showPlaceBottomSheet(place);
  }

  void _showPlaceBottomSheet(Place place) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildPlaceBottomSheet(place),
    );
  }

  Widget _buildPlaceBottomSheet(Place place) {
    String distance = '';
    if (_currentPosition != null &&
        place.latitude != null &&
        place.longitude != null) {
      double distanceKm = LocationService.calculateDistance(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        place.latitude!,
        place.longitude!,
      );
      distance = LocationService.formatDistance(distanceKm);
    }

    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          place.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (distance.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppConfig.primary[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            distance,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (place.location != null || place.address != null)
                    Row(
                      children: [
                        const Icon(Icons.location_on,
                            size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            place.location ?? place.address ?? '',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  const SizedBox(height: 12),
                  if (place.description != null)
                    Expanded(
                      child: Text(
                        place.description!,
                        style: const TextStyle(fontSize: 14),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                PlaceDetailScreen(place: place),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppConfig.primary[500],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Voir les détails'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        // Détecter si la catégorie a changé et mettre à jour les lieux filtrés
        if (_lastSelectedCategoryId != categoryProvider.selectedCategoryId &&
            _allNearbyPlaces.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final filteredPlaces =
                categoryProvider.filterNearbyPlaces(_allNearbyPlaces);
            setState(() {
              _nearbyPlaces = filteredPlaces;
              _lastSelectedCategoryId = categoryProvider.selectedCategoryId;
            });
            _createMarkers();
          });
        }

        return Scaffold(
          body: RefreshIndicator(
            onRefresh: _loadMapData,
            child: Stack(
              children: [
                // Carte OpenStreetMap
                FlutterMap(
                  mapController: _mapController,
                  options: MapOptions(
                    initialCenter: _currentPosition != null
                        ? LatLng(_currentPosition!.latitude,
                            _currentPosition!.longitude)
                        : _defaultLocation,
                    initialZoom: 12.0,
                    minZoom: 5.0,
                    maxZoom: 18.0,
                  ),
                  children: [
                    // Couche de tuiles OpenStreetMap
                    TileLayer(
                      urlTemplate:
                          'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                      userAgentPackageName: 'com.example.mboka_tour_app',
                      maxZoom: 18,
                    ),
                    // Couche de marqueurs
                    MarkerLayer(
                      markers: _markers,
                    ),
                  ],
                ),

                // Overlay de chargement
                if (_isLoading)
                  Container(
                    color: Colors.black.withValues(alpha: 0.3),
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: Colors.white),
                          SizedBox(height: 16),
                          Text(
                            'Chargement des lieux à proximité...',
                            style: TextStyle(color: Colors.white, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Message d'erreur
                if (_errorMessage != null && !_isLoading)
                  Positioned(
                    top: 100,
                    left: 16,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.error, color: Colors.white),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          IconButton(
                            onPressed: _loadMapData,
                            icon:
                                const Icon(Icons.refresh, color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Compteur de résultats
                Positioned(
                  top: MediaQuery.of(context).padding.top + 16,
                  left: 16,
                  child: _buildResultsCounter(),
                ),

                // Boutons de contrôle de la carte
                Positioned(
                  right: 16,
                  bottom: 100,
                  child: Column(
                    children: [
                      FloatingActionButton(
                        mini: true,
                        heroTag: "zoom_in",
                        onPressed: () {
                          _mapController.move(_mapController.camera.center,
                              _mapController.camera.zoom + 1);
                        },
                        backgroundColor: Colors.white,
                        child: const Icon(Icons.add, color: Colors.black),
                      ),
                      const SizedBox(height: 8),
                      FloatingActionButton(
                        mini: true,
                        heroTag: "zoom_out",
                        onPressed: () {
                          _mapController.move(_mapController.camera.center,
                              _mapController.camera.zoom - 1);
                        },
                        backgroundColor: Colors.white,
                        child: const Icon(Icons.remove, color: Colors.black),
                      ),
                      const SizedBox(height: 8),
                      FloatingActionButton(
                        mini: true,
                        heroTag: "my_location",
                        onPressed: () {
                          if (_currentPosition != null) {
                            _mapController.move(
                              LatLng(_currentPosition!.latitude,
                                  _currentPosition!.longitude),
                              15.0,
                            );
                          }
                        },
                        backgroundColor: AppConfig.primary[500],
                        child:
                            const Icon(Icons.my_location, color: Colors.white),
                      ),
                      const SizedBox(height: 8),
                      FloatingActionButton(
                        mini: true,
                        heroTag: "refresh",
                        onPressed: _loadMapData,
                        backgroundColor: Colors.green,
                        child: const Icon(Icons.refresh, color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildResultsCounter() {
    if (_isLoading) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        '${_nearbyPlaces.length} lieu${_nearbyPlaces.length > 1 ? 's' : ''} trouvé${_nearbyPlaces.length > 1 ? 's' : ''}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
