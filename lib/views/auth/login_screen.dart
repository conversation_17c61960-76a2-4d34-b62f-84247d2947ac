import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/controllers/auth_controller.dart';
import 'package:mbokatour/repositories/auth_repository.dart';
import 'package:mbokatour/services/api_service.dart';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/views/auth/register_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _obscurePassword = true;

  late AuthController _authController;

  @override
  void initState() {
    super.initState();
    _authController = AuthController(AuthRepository(ApiService.instance.dio));
  }

  @override
  void dispose() {
    _loginController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String _getErrorMessage(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connexion lente. Vérifiez votre connexion internet.';
        case DioExceptionType.connectionError:
          return 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final responseData = error.response?.data;

          if (statusCode == 401) {
            return 'Identifiants incorrects. Vérifiez votre téléphone/email et mot de passe.';
          } else if (statusCode == 422) {
            // Erreurs de validation
            if (responseData is Map && responseData.containsKey('message')) {
              return responseData['message'];
            }
            return 'Données invalides. Vérifiez vos informations.';
          } else if (statusCode == 429) {
            return 'Trop de tentatives. Veuillez patienter avant de réessayer.';
          } else if (statusCode == 500) {
            return 'Erreur du serveur. Veuillez réessayer plus tard.';
          }
          return 'Erreur de connexion (Code: $statusCode)';
        case DioExceptionType.cancel:
          return 'Connexion annulée.';
        case DioExceptionType.unknown:
          return 'Erreur de réseau. Vérifiez votre connexion internet.';
        default:
          return 'Erreur de connexion inattendue.';
      }
    }

    // Erreurs génériques
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('failed to login') ||
        errorString.contains('unauthorized')) {
      return 'Identifiants incorrects. Vérifiez votre téléphone/email et mot de passe.';
    } else if (errorString.contains('network') ||
        errorString.contains('connection')) {
      return 'Problème de connexion. Vérifiez votre connexion internet.';
    } else if (errorString.contains('timeout')) {
      return 'Connexion trop lente. Veuillez réessayer.';
    }

    return 'Erreur de connexion. Veuillez réessayer.';
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _authController.login(
        login: _loginController.text.trim(),
        password: _passwordController.text,
      );

      // Sauvegarder le token et les données utilisateur
      await AuthService.instance.saveAuth(response.accessToken, response.user);

      if (mounted) {
        // Afficher un message de succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Connexion réussie ! Bienvenue ${response.user.name}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // Retourner true pour indiquer que la connexion a réussi
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        final errorMessage = _getErrorMessage(e);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Réessayer',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppConfig.primary[700]),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                Text(
                  'Connexion',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: AppConfig.primary[800],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Connectez-vous pour découvrir Kinshasa',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 40),

                // Champ login (téléphone ou email)
                TextFormField(
                  controller: _loginController,
                  decoration: InputDecoration(
                    labelText: 'Téléphone ou Email',
                    hintText: '+243123456789 ou <EMAIL>',
                    prefixIcon:
                        Icon(Icons.person, color: AppConfig.primary[600]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppConfig.primary[500]!),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer votre téléphone ou email';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),

                // Champ mot de passe
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  decoration: InputDecoration(
                    labelText: 'Mot de passe',
                    prefixIcon: Icon(Icons.lock, color: AppConfig.primary[600]),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: AppConfig.primary[600],
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppConfig.primary[500]!),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer votre mot de passe';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 30),

                // Bouton de connexion
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleLogin,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConfig.primary[500],
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? CircularProgressIndicator(color: Colors.white)
                        : Text(
                            'Se connecter',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
                const SizedBox(height: 20),

                // Lien vers inscription
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Pas encore de compte ? ',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const RegisterScreen(),
                          ),
                        );
                      },
                      child: Text(
                        'S\'inscrire',
                        style: TextStyle(
                          color: AppConfig.primary[600],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
