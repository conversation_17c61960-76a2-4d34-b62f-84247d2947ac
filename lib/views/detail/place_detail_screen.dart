import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:heroicons/heroicons.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/models/comment.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:mbokatour/controllers/place_controller.dart';
import 'package:mbokatour/controllers/comment_controller.dart';
import 'package:mbokatour/repositories/place_repository.dart';
import 'package:mbokatour/repositories/comment_repository.dart';
import 'package:mbokatour/services/api_service.dart';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/services/video_cache_service.dart';
import 'package:mbokatour/widgets/auth_required_snackbar.dart';
import 'package:mbokatour/widgets/simple_auth_dialog.dart';
import 'package:mbokatour/widgets/search_style_place_card.dart';
import 'package:mbokatour/views/navigation/route_screen.dart';

class PlaceDetailScreen extends StatefulWidget {
  final Place place;
  final VideoPlayerController?
      videoController; // Ajout du paramètre pour réutiliser le cache

  const PlaceDetailScreen({
    super.key,
    required this.place,
    this.videoController, // Optionnel pour réutiliser le contrôleur existant
  });

  @override
  State<PlaceDetailScreen> createState() => _PlaceDetailScreenState();
}

class _PlaceDetailScreenState extends State<PlaceDetailScreen> {
  VideoPlayerController? _videoController;
  bool _videoInitialized = false;
  bool _videoError = false;
  final PageController _pageController = PageController();
  int _currentImageIndex = 0;

  // Controllers pour l'API
  late PlaceController _placeController;
  late CommentController _commentApiController;

  // Contrôleurs pour l'ajout de commentaires
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  double _userRating = 5.0;
  bool _isSubmittingComment = false;

  // Données chargées depuis l'API
  List<Comment> _comments = [];
  List<Place> _recommendedPlaces = [];
  bool _isLoadingComments = false;
  bool _isLoadingRecommendations = false;

  // État des favoris
  bool _isFavorite = false;
  bool _isLoadingFavorite = false;

  // État des commentaires utilisateur
  bool _hasUserCommented = false;

  @override
  void initState() {
    super.initState();

    // Initialiser les controllers API
    _placeController =
        PlaceController(PlaceRepository(ApiService.instance.dio));
    _commentApiController =
        CommentController(CommentRepository(ApiService.instance.dio));

    // Initialiser la vidéo de manière asynchrone
    _initializeVideo();
    _loadPlaceData();
  }

  Future<void> _loadPlaceData() async {
    // Incrémenter les vues dès l'ouverture des détails (l'API le fait automatiquement)
    _placeController.loadPlaceDetails(widget.place.id).then((_) {
      debugPrint('📈 Vues incrémentées pour: ${widget.place.name}');
    }).catchError((e) {
      debugPrint('Erreur lors de l\'incrémentation des vues: $e');
    });

    // Charger les commentaires, recommandations et état des favoris en parallèle
    await Future.wait([
      _loadComments(),
      _loadRecommendations(),
      _checkFavoriteStatus(),
    ]);
  }

  Future<void> _checkFavoriteStatus() async {
    try {
      // Vérifier si le lieu est déjà dans les favoris
      // On peut utiliser la propriété isFavorited du modèle Place si elle existe
      if (widget.place.isFavorited != null) {
        setState(() {
          _isFavorite = widget.place.isFavorited!;
        });
      } else {
        // Sinon, charger tous les favoris et vérifier
        final favorites = await _placeController.getFavoritePlaces();
        if (mounted) {
          setState(() {
            _isFavorite = favorites.any((place) => place.id == widget.place.id);
          });
        }
      }
    } catch (e) {
      // En cas d'erreur, on assume que ce n'est pas un favori
      debugPrint('Erreur lors de la vérification des favoris: $e');
      if (mounted) {
        setState(() {
          _isFavorite = false;
        });
      }
    }
  }

  Future<void> _loadComments() async {
    try {
      setState(() {
        _isLoadingComments = true;
      });

      final comments =
          await _commentApiController.getCommentsByPlaceId(widget.place.id);

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoadingComments = false;

          // Vérifier si l'utilisateur connecté a déjà commenté
          if (AuthService.instance.isAuthenticated &&
              AuthService.instance.currentUser != null) {
            final currentUserName = AuthService.instance.currentUser!.name;
            _hasUserCommented =
                comments.any((comment) => comment.userName == currentUserName);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingComments = false;
        });
        debugPrint('Erreur lors du chargement des commentaires: $e');
      }
    }
  }

  Future<void> _loadRecommendations() async {
    try {
      setState(() {
        _isLoadingRecommendations = true;
      });

      // Charger les lieux recommandés
      final recommendations = await _placeController.getDiscover();

      if (mounted) {
        setState(() {
          // Filtrer et mélanger les recommandations pour plus de variété
          final filteredPlaces = recommendations
              .where((place) => place.id != widget.place.id)
              .toList();

          // Mélanger la liste pour avoir des recommandations variées
          filteredPlaces.shuffle();

          // Prendre les 4 premiers lieux pour avoir plus de choix
          _recommendedPlaces = filteredPlaces.take(4).toList();
          _isLoadingRecommendations = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingRecommendations = false;
        });
        debugPrint('Erreur lors du chargement des recommandations: $e');

        // Afficher un message d'erreur à l'utilisateur
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.warning, color: Colors.white),
                const SizedBox(width: 8),
                const Text('Impossible de charger les recommandations'),
              ],
            ),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _initializeVideo() async {
    if (widget.place.mediaType == 'video' &&
        widget.place.mainImageUrl != null) {
      // 1. Réutiliser le contrôleur existant si disponible et initialisé
      if (widget.videoController != null &&
          widget.videoController!.value.isInitialized) {
        _videoController = widget.videoController;
        _videoInitialized = true;
        return;
      }

      try {
        // 2. Utiliser le service de cache pour obtenir un contrôleur optimisé
        final videoCacheService = VideoCacheService();
        final cachedController = await videoCacheService.getVideoController(
          widget.place.mainImageUrl!,
          widget.place.id,
        );

        if (cachedController != null) {
          _videoController = cachedController;

          // Initialiser le contrôleur si ce n'est pas déjà fait
          if (!_videoController!.value.isInitialized) {
            await _videoController!.initialize();
          }

          if (mounted) {
            setState(() {
              _videoInitialized = true;
              _videoError = false;
            });
            _videoController!.setLooping(true);
          }
        } else {
          throw Exception(
              'Impossible d\'obtenir le contrôleur vidéo depuis le cache');
        }
      } catch (error) {
        debugPrint('Erreur lors de l\'initialisation de la vidéo: $error');

        if (mounted) {
          setState(() {
            _videoError = true;
            _videoInitialized = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    // Ne disposer le contrôleur que s'il n'est pas réutilisé depuis la page d'accueil
    if (_videoController != null &&
        _videoController != widget.videoController) {
      _videoController!.dispose();
    }
    _pageController.dispose();
    _commentController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> get _mediaItems {
    final List<Map<String, dynamic>> items = [];

    // Ajouter la vidéo principale si elle existe
    if (widget.place.mediaType == 'video' &&
        widget.place.mainImageUrl != null) {
      items.add({
        'type': 'video',
        'url': widget.place.mainImageUrl!,
        'controller': _videoController,
      });
    }

    // Ajouter l'image principale si ce n'est pas une vidéo
    if (widget.place.mediaType != 'video' &&
        widget.place.mainImageUrl != null) {
      items.add({
        'type': 'image',
        'url': widget.place.mainImageUrl!,
      });
    }

    // Ajouter les autres images
    if (widget.place.images != null) {
      for (var img in widget.place.images!) {
        items.add({
          'type': 'image',
          'url': img.imageUrl,
        });
      }
    }

    return items;
  }

  Widget _buildVideoPlayer(Map<String, dynamic> item) {
    final controller = item['controller'] as VideoPlayerController?;

    // Si il y a une erreur, afficher une image de fallback
    if (_videoError) {
      return _buildVideoFallback(item['url']);
    }

    // Si la vidéo est initialisée, l'afficher
    if (controller != null &&
        _videoInitialized &&
        controller.value.isInitialized) {
      return Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: FittedBox(
              fit: BoxFit.cover, // Couvre tout l'espace disponible
              child: SizedBox(
                width: controller.value.size.width,
                height: controller.value.size.height,
                child: VideoPlayer(controller),
              ),
            ),
          ),
          // Overlay avec contrôles - Zone de clic simplifiée
          Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  debugPrint(
                      '🎬 Clic vidéo détecté - État actuel: ${controller.value.isPlaying}');
                  _toggleVideoPlayback(controller);
                },
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.2),
                      ],
                    ),
                  ),
                  child: Center(
                    child: AnimatedOpacity(
                      opacity: controller.value.isPlaying ? 0.0 : 1.0,
                      duration: const Duration(milliseconds: 300),
                      child: AnimatedScale(
                        scale: controller.value.isPlaying ? 0.8 : 1.0,
                        duration: const Duration(milliseconds: 200),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.4),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 200),
                            child: Icon(
                              controller.value.isPlaying
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              key: ValueKey(controller.value.isPlaying),
                              color: Colors.white,
                              size: 48,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Indicateur de chargement si la vidéo est en cours de buffering
          if (controller.value.isBuffering)
            Positioned(
              bottom: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            ),
        ],
      );
    }

    // Afficher un indicateur de chargement
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Chargement de la vidéo...',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoFallback(String? videoUrl) {
    // Essayer d'afficher une image de fallback ou une miniature
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Essayer d'afficher une image de fallback depuis les autres images du lieu
          if (widget.place.images != null && widget.place.images!.isNotEmpty)
            CachedNetworkImage(
              imageUrl: widget.place.images!.first.imageUrl,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorWidget: (context, url, error) =>
                  _buildDefaultVideoFallback(),
            )
          else
            _buildDefaultVideoFallback(),
          // Overlay indiquant que c'est une vidéo
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_outline,
                size: 64,
                color: Colors.white,
              ),
              const SizedBox(height: 8),
              const Text(
                'Vidéo non disponible',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              TextButton(
                onPressed: () {
                  // Réessayer de charger la vidéo
                  setState(() {
                    _videoError = false;
                    _videoInitialized = false;
                  });
                  _initializeVideo();
                },
                child: const Text(
                  'Réessayer',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultVideoFallback() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: const Icon(
        Icons.videocam_off,
        size: 48,
        color: Colors.grey,
      ),
    );
  }

  // Méthode pour soumettre un commentaire
  Future<void> _submitComment() async {
    // Validation du contenu
    if (_commentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.warning, color: Colors.white),
              const SizedBox(width: 8),
              const Text('Veuillez saisir un commentaire'),
            ],
          ),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Validation de la note
    if (_userRating <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.star_border, color: Colors.white),
              const SizedBox(width: 8),
              const Text('Veuillez donner une note (1-5 étoiles)'),
            ],
          ),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Vérifier si l'utilisateur est connecté
    if (!context.checkAuthAndShowSnackbar(
      isAuthenticated: AuthService.instance.isAuthenticated,
      message: 'Vous devez être connecté pour laisser un commentaire',
      onLoginSuccess: () {
        // Recharger les données après connexion
        _loadPlaceData();
      },
    )) {
      return;
    }

    // Vérifier la validité du token
    final authService = AuthService.instance;
    if (authService.currentToken == null || authService.currentToken!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              const Text('Session expirée, veuillez vous reconnecter'),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isSubmittingComment = true;
    });

    try {
      // Debug: Afficher les informations de la requête
      debugPrint('🔄 Soumission commentaire:');
      debugPrint('   - Place ID: ${widget.place.id}');
      debugPrint('   - Contenu: ${_commentController.text.trim()}');
      debugPrint('   - Note: $_userRating');
      debugPrint(
          '   - Token: ${authService.currentToken?.substring(0, 20)}...');

      // Ajouter le commentaire via l'API
      final newComment = await _commentApiController.addComment(
        placeId: widget.place.id,
        content: _commentController.text.trim(),
        rating: _userRating,
      );

      debugPrint('✅ Commentaire ajouté avec succès: ${newComment.id}');

      if (mounted) {
        // Ajouter le nouveau commentaire à la liste
        setState(() {
          _comments.insert(0, newComment); // Ajouter en premier
          _userRating = 0; // Réinitialiser la note
        });

        // Afficher un message de succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Text('Commentaire ajouté avec succès !'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Vider le champ de commentaire
        _commentController.clear();
        _commentFocusNode.unfocus();
      }
    } catch (error) {
      debugPrint('❌ Erreur lors de l\'ajout du commentaire: $error');

      if (mounted) {
        String errorMessage = 'Erreur lors de l\'ajout du commentaire';
        Color errorColor = Colors.red;

        // Analyser le type d'erreur pour donner un message plus précis
        if (error.toString().contains('ALREADY_COMMENTED')) {
          errorMessage = 'Vous avez déjà commenté ce lieu';
          errorColor = Colors.blue;
        } else if (error.toString().contains('401')) {
          errorMessage = 'Session expirée, veuillez vous reconnecter';
          errorColor = Colors.orange;
        } else if (error.toString().contains('422')) {
          errorMessage = 'Données invalides, vérifiez votre commentaire';
          errorColor = Colors.orange;
        } else if (error.toString().contains('403')) {
          errorMessage =
              'Vous n\'avez pas l\'autorisation d\'ajouter un commentaire';
          errorColor = Colors.red;
        } else if (error.toString().contains('404')) {
          errorMessage = 'Lieu introuvable';
          errorColor = Colors.red;
        } else if (error.toString().contains('Failed to add comment')) {
          errorMessage =
              'Impossible d\'ajouter le commentaire. Vérifiez votre connexion.';
          errorColor = Colors.red;
        }

        // Gestion spéciale pour le cas "déjà commenté"
        if (error.toString().contains('ALREADY_COMMENTED')) {
          // Mettre à jour l'état pour indiquer que l'utilisateur a déjà commenté
          setState(() {
            _hasUserCommented = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.comment, color: Colors.white),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                        'Vous avez déjà commenté ce lieu. Un seul commentaire par lieu est autorisé.'),
                  ),
                ],
              ),
              backgroundColor: Colors.blue,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: 'Voir mes commentaires',
                textColor: Colors.white,
                onPressed: () {
                  // Scroll vers la section commentaires pour voir le commentaire existant
                  _showAllCommentsBottomSheet(context);
                },
              ),
            ),
          );
        } else {
          // Gestion normale des autres erreurs
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.error, color: Colors.white),
                      const SizedBox(width: 8),
                      Expanded(child: Text(errorMessage)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Détails: ${error.toString()}',
                    style: const TextStyle(fontSize: 12, color: Colors.white70),
                  ),
                ],
              ),
              backgroundColor: errorColor,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 6),
              action: SnackBarAction(
                label: 'Réessayer',
                textColor: Colors.white,
                onPressed: () => _submitComment(),
              ),
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmittingComment = false;
        });
      }
    }
  }

  // Méthode pour afficher tous les commentaires dans un bottom sheet
  void _showAllCommentsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // Handle du bottom sheet
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Header
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      const Text(
                        'Tous les commentaires',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),

                // Statistiques des avis
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Column(
                        children: [
                          const Text(
                            '4.7',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Row(
                            children: List.generate(
                              5,
                              (index) => Icon(
                                index < 4 ? Icons.star : Icons.star_border,
                                color: Colors.amber,
                                size: 16,
                              ),
                            ),
                          ),
                          const Text(
                            '2,498 avis',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          children: [
                            _buildRatingBar(5, 0.7),
                            _buildRatingBar(4, 0.2),
                            _buildRatingBar(3, 0.05),
                            _buildRatingBar(2, 0.03),
                            _buildRatingBar(1, 0.02),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Liste des commentaires
                Expanded(
                  child: ListView.separated(
                    controller: scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    itemCount: _getAllComments().length,
                    separatorBuilder: (context, index) =>
                        const Divider(height: 24),
                    itemBuilder: (context, index) {
                      final comment = _getAllComments()[index];
                      return _buildCommentItem(comment);
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Méthode pour naviguer vers la page d'itinéraire
  void _navigateToRoute() {
    // Vérifier si le lieu a des coordonnées
    if (widget.place.latitude == null || widget.place.longitude == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.warning, color: Colors.white),
              const SizedBox(width: 8),
              const Text('Coordonnées du lieu non disponibles'),
            ],
          ),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Naviguer vers la page d'itinéraire
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RouteScreen(destination: widget.place),
      ),
    );
  }

  // Méthode pour construire le floating action button de navigation
  Widget _buildNavigationFAB() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: FloatingActionButton.extended(
        onPressed: _navigateToRoute,
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 8,
        icon: const Icon(Icons.navigation, size: 20),
        label: const Text(
          'Navigation GPS',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  Widget _buildImageWidget(String url) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: CachedNetworkImage(
        imageUrl: url,
        fit: BoxFit.cover, // Optimisé pour remplir l'espace en format portrait
        placeholder: (context, url) => Container(
          color: Colors.grey[100],
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text(
                  'Chargement de l\'image...',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[300],
          child: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.broken_image, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text(
                'Image non disponible',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
        // Optimisation pour les images portrait
        imageBuilder: (context, imageProvider) {
          return Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
                alignment: Alignment.center,
              ),
            ),
          );
        },
      ),
    );
  }

  // Méthode pour construire une miniature de vidéo dans la galerie
  Widget _buildVideoThumbnail(Map<String, dynamic> item) {
    final controller = item['controller'] as VideoPlayerController?;

    // Si le contrôleur vidéo est disponible et initialisé, afficher la première frame
    if (controller != null && controller.value.isInitialized) {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: FittedBox(
          fit: BoxFit.cover,
          child: SizedBox(
            width: controller.value.size.width,
            height: controller.value.size.height,
            child: VideoPlayer(controller),
          ),
        ),
      );
    }

    // Sinon, afficher l'URL comme une image (thumbnail)
    return _buildImageWidget(item['url']);
  }

  @override
  Widget build(BuildContext context) {
    final place = widget.place;
    final mediaItems = _mediaItems;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      floatingActionButton: _buildNavigationFAB(),
      body: CustomScrollView(
        slivers: [
          // Header Transparent avec Overlay - Design Premium
          SliverAppBar(
            expandedHeight:
                400, // Hauteur augmentée pour une meilleure présentation
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: Container(
              margin: const EdgeInsets.all(8.0),
              child: ClipOval(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.share, color: Colors.white),
                  onPressed: () => _sharePlace(),
                ),
              ),
              Container(
                margin: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                ),
                child: _isLoadingFavorite
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : IconButton(
                        icon: Icon(
                          _isFavorite ? Icons.favorite : Icons.favorite_border,
                          color: _isFavorite ? Colors.red : Colors.white,
                        ),
                        onPressed: () => _toggleFavorite(),
                      ),
              ),
              const SizedBox(width: 8),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: ClipRRect(
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
                child: SizedBox(
                  height: 475, // Hauteur fixe pour une meilleure organisation
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Média principal avec navigation par swipe
                      if (mediaItems.isNotEmpty)
                        PageView.builder(
                          onPageChanged: (index) {
                            setState(() {
                              _currentImageIndex = index;
                            });
                          },
                          itemCount: mediaItems.length,
                          itemBuilder: (context, index) {
                            final item = mediaItems[index];
                            return item['type'] == 'video'
                                ? _buildVideoPlayer(item)
                                : _buildImageWidget(item['url']);
                          },
                        )
                      else
                        Container(
                          height: 400,
                          color: Colors.grey[300],
                          child: const Center(
                            child:
                                Icon(Icons.image, size: 64, color: Colors.grey),
                          ),
                        ),

                      // Gradient overlay renforcé pour améliorer la lisibilité
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withValues(alpha: 0.4),
                              Colors.black.withValues(alpha: 0.1),
                              Colors.black.withValues(alpha: 0.3),
                              Colors.black.withValues(alpha: 0.8),
                            ],
                            stops: const [0.0, 0.4, 0.7, 1.0],
                          ),
                        ),
                      ),

                      // Informations du lieu en bas
                      Positioned(
                        bottom: 20,
                        left: 20,
                        right: 20,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                place.name,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black87,
                                      offset: Offset(0, 2),
                                      blurRadius: 8,
                                    ),
                                    Shadow(
                                      color: Colors.black54,
                                      offset: Offset(1, 1),
                                      blurRadius: 4,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            if (place.address != null)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.location_on,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 4),
                                    Flexible(
                                      child: Text(
                                        place.address!,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black87,
                                              offset: Offset(0, 1),
                                              blurRadius: 4,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            const SizedBox(height: 8),
                            // Affichage des vues et likes
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Colors.black.withValues(alpha: 0.4),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Vues
                                  const Icon(
                                    Icons.visibility,
                                    color: Colors.white70,
                                    size: 14,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${place.viewsCount ?? 0}',
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black87,
                                          offset: Offset(0, 1),
                                          blurRadius: 4,
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  // Likes
                                  HeroIcon(
                                    HeroIcons.handThumbUp,
                                    style: HeroIconStyle.outline,
                                    color: Colors.white70,
                                    size: 14,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${place.likesCount ?? 0}',
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black87,
                                          offset: Offset(0, 1),
                                          blurRadius: 4,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Galerie d'images supplémentaires (si plus d'une image)
          _buildMediaGallerySliver(mediaItems),

          // Section Description - Design Minimaliste
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppConfig.primary[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.info_outline,
                          color: AppConfig.primary[600],
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'À propos de ce lieu',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Text(
                    place.description ?? 'Aucune description disponible.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[700],
                      height: 1.6,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Affichage des catégories
                  if (place.categories != null && place.categories!.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Catégories',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: place.categories!
                              .map((category) => Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: _getCategoryColor(category.color)
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: _getCategoryColor(category.color)
                                            .withValues(alpha: 0.3),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        if (category.icon != null)
                                          Icon(
                                            _getCategoryIcon(category.icon!),
                                            size: 16,
                                            color: _getCategoryColor(
                                                category.color),
                                          ),
                                        if (category.icon != null)
                                          const SizedBox(width: 6),
                                        Text(
                                          category.name,
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: _getCategoryColor(
                                                category.color),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ))
                              .toList(),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),

                  // Informations détaillées en grid
                  _buildLocationInfoGrid(place),

                  const SizedBox(height: 12),

                  // Prix en pleine largeur
                  _buildPriceCard(place),
                ],
              ),
            ),
          ),

          // Section Commentaires - Design Simple
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Commentaires',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          _showAllCommentsBottomSheet(context);
                        },
                        child: const Text('Voir tout'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Section pour ajouter un commentaire
                  _buildAddCommentSection(),

                  const SizedBox(height: 24),
                  Divider(color: Colors.grey[300]),
                  const SizedBox(height: 20),

                  // Commentaires existants
                  const Text(
                    'Avis des visiteurs',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Affichage des commentaires depuis l'API
                  if (_isLoadingComments)
                    const Center(
                      child: Padding(
                        padding: EdgeInsets.all(20),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  else if (_comments.isEmpty)
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          Icon(Icons.comment_outlined,
                              color: Colors.grey[400], size: 32),
                          const SizedBox(height: 8),
                          Text(
                            'Aucun commentaire pour le moment',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Soyez le premier à laisser un avis !',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    // Afficher les 2 premiers commentaires
                    ...List.generate(
                      _comments.length > 2 ? 2 : _comments.length,
                      (index) => Column(
                        children: [
                          _buildCommentItem(_comments[index]),
                          if (index <
                              (_comments.length > 2 ? 1 : _comments.length - 1))
                            Container(
                              margin: const EdgeInsets.symmetric(vertical: 16),
                              height: 1,
                              color: Colors.grey[200],
                            ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Section Recommandations - Design Minimaliste
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.purple.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.explore_outlined,
                            color: Colors.purple,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'Lieux recommandés',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    _isLoadingRecommendations
                        ? const SizedBox(
                            height: 100,
                            child: Center(child: CircularProgressIndicator()),
                          )
                        : _recommendedPlaces.isEmpty
                            ? Container(
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Colors.grey[50],
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.grey[200]!),
                                ),
                                padding: const EdgeInsets.all(20),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.explore_off,
                                        color: Colors.grey[400], size: 32),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Aucune recommandation disponible',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    TextButton(
                                      onPressed: () {
                                        // Réessayer de charger les recommandations
                                        _loadRecommendations();
                                      },
                                      child: const Text('Réessayer'),
                                    ),
                                  ],
                                ),
                              )
                            : Column(
                                children: _recommendedPlaces
                                    .map((place) => SearchStylePlaceCard(
                                          place: place,
                                          margin:
                                              const EdgeInsets.only(bottom: 8),
                                        ))
                                    .toList(),
                              ),
                  ],
                ),
              ),
            ),
          ),

          // Espace en bas pour éviter que le contenu soit coupé
          const SliverToBoxAdapter(
            child: SizedBox(height: 20),
          ),
        ],
      ),
    );
  }

  // Méthode pour construire la grille d'informations de localisation
  Widget _buildLocationInfoGrid(Place place) {
    List<Widget> infoCards = [];

    // Ville
    if (place.city != null && place.city!.isNotEmpty) {
      infoCards.add(_buildInfoCard(
        icon: Icons.location_city,
        title: 'Ville',
        value: place.city!,
        color: Colors.blue,
      ));
    }

    // Quartier
    if (place.neighborhood != null && place.neighborhood!.isNotEmpty) {
      infoCards.add(_buildInfoCard(
        icon: Icons.home,
        title: 'Quartier',
        value: place.neighborhood!,
        color: Colors.purple,
      ));
    }

    // Adresse complète
    if (place.address != null && place.address!.isNotEmpty) {
      infoCards.add(_buildInfoCard(
        icon: Icons.location_on,
        title: 'Adresse',
        value: place.address!,
        color: Colors.red,
      ));
    }

    // Horaires d'ouverture
    if (place.openingHours != null && place.openingHours!.isNotEmpty) {
      infoCards.add(_buildInfoCard(
        icon: Icons.access_time,
        title: 'Horaires',
        value: place.openingHours!,
        color: Colors.orange,
      ));
    }

    // Note (données statiques pour l'instant)
    infoCards.add(_buildInfoCard(
      icon: Icons.star,
      title: 'Note',
      value: '4.7 (2,498 avis)',
      color: Colors.amber,
    ));

    // Localisation générale (fallback)
    if (place.city == null &&
        place.neighborhood == null &&
        place.address == null) {
      infoCards.add(_buildInfoCard(
        icon: Icons.location_on,
        title: 'Localisation',
        value: place.location ?? 'Non spécifiée',
        color: Colors.red,
      ));
    }

    // Organiser en grille 2 colonnes
    List<Widget> rows = [];
    for (int i = 0; i < infoCards.length; i += 2) {
      if (i + 1 < infoCards.length) {
        // Deux cartes côte à côte
        rows.add(Row(
          children: [
            Expanded(child: infoCards[i]),
            const SizedBox(width: 12),
            Expanded(child: infoCards[i + 1]),
          ],
        ));
      } else {
        // Une seule carte (pleine largeur)
        rows.add(infoCards[i]);
      }

      if (i + 2 < infoCards.length) {
        rows.add(const SizedBox(height: 12));
      }
    }

    return Column(children: rows);
  }

  // Méthodes helper pour le nouveau design minimaliste
  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 6),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceCard(Place place) {
    final isGratuit = place.price == null;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isGratuit
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isGratuit
              ? Colors.green.withValues(alpha: 0.2)
              : Colors.blue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isGratuit ? Icons.card_giftcard : Icons.payments,
            color: isGratuit ? Colors.green : Colors.blue,
            size: 20,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Prix',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                isGratuit ? 'Gratuit' : '${place.price} CDF / Personne',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isGratuit ? Colors.green : Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Méthode pour construire une barre de notation
  Widget _buildRatingBar(int stars, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$stars',
            style: const TextStyle(fontSize: 12),
          ),
          const SizedBox(width: 8),
          Icon(Icons.star, color: Colors.amber, size: 12),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
              minHeight: 4,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(percentage * 100).toInt()}%',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // Méthode pour obtenir tous les commentaires depuis l'API
  List<Comment> _getAllComments() {
    return _comments;
  }

  Widget _buildAddCommentSection() {
    // Si l'utilisateur a déjà commenté, afficher un message simple
    if (_hasUserCommented) {
      return Column(
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Vous avez déjà commenté ce lieu',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: () => _showAllCommentsBottomSheet(context),
            child: const Text('Voir mes commentaires'),
          ),
        ],
      );
    }

    // Formulaire simple pour ajouter un commentaire
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ajouter votre avis',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Système de notation par étoiles simple
        Row(
          children: [
            const Text(
              'Note: ',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            ...List.generate(5, (index) {
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _userRating = (index + 1).toDouble();
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Icon(
                    index < _userRating ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 24,
                  ),
                ),
              );
            }),
            const SizedBox(width: 8),
            Text(
              '${_userRating.toInt()}/5',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Champ de texte simple
        TextField(
          controller: _commentController,
          focusNode: _commentFocusNode,
          maxLines: 3,
          maxLength: 500,
          decoration: InputDecoration(
            hintText: 'Partagez votre expérience...',
            hintStyle: TextStyle(color: Colors.grey[500]),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppConfig.primary[500]!, width: 2),
            ),
            contentPadding: const EdgeInsets.all(12),
          ),
        ),

        const SizedBox(height: 16),

        // Bouton simple
        ElevatedButton(
          onPressed: _isSubmittingComment ? null : _submitComment,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConfig.primary[500],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isSubmittingComment
              ? const SizedBox(
                  height: 16,
                  width: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Publier'),
        ),
      ],
    );
  }

  Widget _buildCommentItem(Comment comment) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 20,
          backgroundImage: CachedNetworkImageProvider(comment.defaultAvatar),
          backgroundColor: Colors.grey[300],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    comment.userName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Icon(Icons.star, color: Colors.amber, size: 14),
                      Text(
                        comment.rating.toString(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                comment.content,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Il y a ${comment.timeAgo}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Méthode pour ouvrir la galerie d'images en plein écran
  void _openImageGallery(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ImageGalleryScreen(
          mediaItems: _mediaItems,
          initialIndex: initialIndex,
          placeName: widget.place.name,
        ),
      ),
    );
  }

  // Méthode pour partager le lieu (partage direct)
  void _sharePlace() {
    // Créer l'URL universelle avec fallback
    final String universalLink =
        'https://mbokatour.com/place/${widget.place.id}';

    final String shareText = '''
🏛️ ${widget.place.name}

📍 ${widget.place.address ?? 'Kinshasa, RDC'}

${widget.place.description ?? 'Découvrez ce lieu incroyable à Kinshasa !'}

💰 ${widget.place.price != null ? '${widget.place.price} CDF / Personne' : 'Gratuit'}

👆 Voir cette place : $universalLink

Découvrez Kinshasa avec MbokaTour ! 🇨🇩
''';

    // Partage direct du lien
    _shareDirectLink(shareText, universalLink);
  }

  // Méthode pour partage direct du lien
  void _shareDirectLink(String shareText, String universalLink) {
    // Copier le texte complet dans le presse-papiers
    Clipboard.setData(ClipboardData(text: shareText));

    // Afficher confirmation avec option de voir le lien
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.share, color: Colors.white),
            const SizedBox(width: 8),
            const Expanded(
              child:
                  Text('Lien de partage copié ! Collez-le dans vos messages.'),
            ),
          ],
        ),
        backgroundColor: AppConfig.primary[600],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'Voir le lien',
          textColor: Colors.white,
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.link, color: AppConfig.primary[600]),
                    const SizedBox(width: 8),
                    const Text('Lien de partage'),
                  ],
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('Lien universel (app + web) :'),
                    const SizedBox(height: 12),
                    SelectableText(
                      universalLink,
                      style: TextStyle(
                        fontFamily: 'monospace',
                        color: AppConfig.primary[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                            color: Colors.blue.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.info, color: Colors.blue, size: 16),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'Ce lien ouvre l\'app si installée, sinon le site web.',
                              style:
                                  TextStyle(fontSize: 12, color: Colors.blue),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Fermer'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: universalLink));
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Lien copié !'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConfig.primary[500],
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Copier le lien'),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Méthode pour construire la galerie de médias comme sliver
  Widget _buildMediaGallerySliver(List<Map<String, dynamic>> mediaItems) {
    if (mediaItems.length <= 1) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            SizedBox(
              height: 100, // Hauteur optimisée pour une meilleure visibilité
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: mediaItems.length,
                itemBuilder: (context, index) {
                  final item = mediaItems[index];
                  return GestureDetector(
                    onTap: () => _openImageGallery(index),
                    child: Container(
                      width: 100,
                      margin: const EdgeInsets.only(right: 12),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.15),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                        border: Border.all(
                          color: _currentImageIndex == index
                              ? AppConfig.primary[500]!
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            // Afficher l'image ou la vidéo selon le type
                            item['type'] == 'video'
                                ? _buildVideoThumbnail(item)
                                : _buildImageWidget(item['url']),
                            if (item['type'] == 'video')
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.4),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Center(
                                  child: Icon(
                                    Icons.play_circle_filled,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                ),
                              ),
                            // Indicateur de sélection
                            if (_currentImageIndex == index)
                              Container(
                                decoration: BoxDecoration(
                                  color: AppConfig.primary[500]!
                                      .withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Center(
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: AppConfig.primary[500],
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthode pour basculer la lecture/pause de la vidéo avec feedback
  void _toggleVideoPlayback(VideoPlayerController controller) {
    if (!mounted) return;

    try {
      setState(() {
        if (controller.value.isPlaying) {
          controller.pause();
          // Feedback haptique léger
          HapticFeedback.lightImpact();
        } else {
          controller.play();
          // Feedback haptique léger
          HapticFeedback.lightImpact();
        }
      });

      // Afficher brièvement l'icône de contrôle
      _showVideoControlFeedback(controller.value.isPlaying);
    } catch (e) {
      debugPrint('Erreur lors du contrôle vidéo: $e');
      // En cas d'erreur, afficher un message discret
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Erreur de lecture vidéo'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  // Méthode pour afficher un feedback visuel lors du contrôle vidéo
  void _showVideoControlFeedback(bool isPlaying) {
    // Cette méthode peut être étendue pour ajouter des animations
    // ou des indicateurs visuels supplémentaires
    if (mounted) {
      // Animation déjà gérée par AnimatedOpacity et AnimatedScale
      // Possibilité d'ajouter d'autres feedbacks ici
    }
  }

  // Méthodes helper pour les catégories
  Color _getCategoryColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return AppConfig.primary[500]!; // Couleur par défaut
    }

    // Si c'est un nom de couleur
    switch (colorString.toLowerCase()) {
      case 'red':
        return Colors.red;
      case 'blue':
        return Colors.blue;
      case 'green':
        return Colors.green;
      case 'orange':
        return Colors.orange;
      case 'purple':
        return Colors.purple;
      case 'pink':
        return Colors.pink;
      case 'teal':
        return Colors.teal;
      case 'amber':
        return Colors.amber;
      case 'indigo':
        return Colors.indigo;
      case 'cyan':
        return Colors.cyan;
      default:
        // Essayer de parser un code couleur hex
        if (colorString.startsWith('#')) {
          try {
            return Color(
                int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
          } catch (e) {
            return AppConfig.primary[500]!;
          }
        }
        return AppConfig.primary[500]!;
    }
  }

  IconData _getCategoryIcon(String iconString) {
    switch (iconString.toLowerCase()) {
      case 'monument':
      case 'monuments':
        return Icons.account_balance;
      case 'museum':
      case 'musée':
      case 'musées':
        return Icons.museum;
      case 'park':
      case 'parc':
      case 'parcs':
      case 'nature':
        return Icons.park;
      case 'restaurant':
      case 'restaurants':
      case 'food':
        return Icons.restaurant;
      case 'shopping':
      case 'shop':
        return Icons.shopping_bag;
      case 'hotel':
      case 'hotels':
        return Icons.hotel;
      case 'adventure':
      case 'aventure':
        return Icons.hiking;
      case 'beach':
      case 'plage':
        return Icons.beach_access;
      case 'church':
      case 'église':
        return Icons.church;
      case 'entertainment':
      case 'divertissement':
        return Icons.local_activity;
      case 'sport':
      case 'sports':
        return Icons.sports;
      case 'culture':
      case 'culturel':
        return Icons.theater_comedy;
      case 'nightlife':
      case 'vie nocturne':
        return Icons.nightlife;
      default:
        return Icons.place; // Icône par défaut
    }
  }

  // Méthode pour basculer l'état des favoris
  Future<void> _toggleFavorite() async {
    // Utiliser le dialog comme alternative plus fiable
    if (!await context.checkAuthAndShowDialog(
      isAuthenticated: AuthService.instance.isAuthenticated,
      title: 'Connexion requise',
      message: 'Vous devez être connecté pour ajouter aux favoris',
      onLoginSuccess: () {
        // Recharger les données après connexion
        _checkFavoriteStatus();
        // Réessayer l'action
        _toggleFavorite();
      },
    )) {
      return;
    }

    setState(() {
      _isLoadingFavorite = true;
    });

    try {
      if (_isFavorite) {
        // Retirer des favoris
        await _placeController.removeFromFavorite(widget.place.id);
        if (mounted) {
          setState(() {
            _isFavorite = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.heart_broken, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('${widget.place.name} retiré des favoris'),
                ],
              ),
              backgroundColor: Colors.grey[600],
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
            ),
          );
        }
      } else {
        // Ajouter aux favoris
        await _placeController.addToFavorite(widget.place.id);
        if (mounted) {
          setState(() {
            _isFavorite = true;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.favorite, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('${widget.place.name} ajouté aux favoris !'),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8)),
            ),
          );
        }
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingFavorite = false;
        });
      }
    }
  }
}

// Écran de galerie d'images en plein écran
class ImageGalleryScreen extends StatefulWidget {
  final List<Map<String, dynamic>> mediaItems;
  final int initialIndex;
  final String placeName;

  const ImageGalleryScreen({
    super.key,
    required this.mediaItems,
    required this.initialIndex,
    required this.placeName,
  });

  @override
  State<ImageGalleryScreen> createState() => _ImageGalleryScreenState();
}

class _ImageGalleryScreenState extends State<ImageGalleryScreen> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.placeName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${_currentIndex + 1} sur ${widget.mediaItems.length}',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {
              // TODO: Implémenter le partage de l'image
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemCount: widget.mediaItems.length,
            itemBuilder: (context, index) {
              final item = widget.mediaItems[index];
              return Center(
                child: InteractiveViewer(
                  panEnabled: true,
                  boundaryMargin: const EdgeInsets.all(20),
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: item['type'] == 'video'
                      ? _buildVideoPlayerFullscreen(item)
                      : _buildImageFullscreen(item['url']),
                ),
              );
            },
          ),

          // Indicateurs de page en bas
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                widget.mediaItems.length,
                (index) => Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentIndex == index
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.5),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageFullscreen(String url) {
    return CachedNetworkImage(
      imageUrl: url,
      fit: BoxFit.contain,
      placeholder: (context, url) => const Center(
        child: CircularProgressIndicator(color: Colors.white),
      ),
      errorWidget: (context, url, error) => const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.broken_image, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Image non disponible',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayerFullscreen(Map<String, dynamic> item) {
    final controller = item['controller'] as VideoPlayerController?;

    // Si le contrôleur vidéo est disponible et initialisé, afficher le lecteur vidéo
    if (controller != null && controller.value.isInitialized) {
      return Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: FittedBox(
              fit: BoxFit.contain, // Pour le plein écran, on utilise contain
              child: SizedBox(
                width: controller.value.size.width,
                height: controller.value.size.height,
                child: VideoPlayer(controller),
              ),
            ),
          ),
          // Contrôles vidéo pour le plein écran
          GestureDetector(
            onTap: () {
              if (controller.value.isPlaying) {
                controller.pause();
                HapticFeedback.lightImpact();
              } else {
                controller.play();
                HapticFeedback.lightImpact();
              }
              setState(() {});
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
              child: Center(
                child: AnimatedOpacity(
                  opacity: controller.value.isPlaying ? 0.0 : 1.0,
                  duration: const Duration(milliseconds: 300),
                  child: AnimatedScale(
                    scale: controller.value.isPlaying ? 0.8 : 1.0,
                    duration: const Duration(milliseconds: 200),
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.5),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          controller.value.isPlaying
                              ? Icons.pause
                              : Icons.play_arrow,
                          key: ValueKey(controller.value.isPlaying),
                          color: Colors.white,
                          size: 60,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          // Indicateur de chargement
          if (controller.value.isBuffering)
            Positioned(
              bottom: 50,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            ),
        ],
      );
    }

    // Fallback : afficher une image avec un bouton play
    return Stack(
      alignment: Alignment.center,
      children: [
        _buildImageFullscreen(item['url']),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(50),
          ),
          child: const Icon(
            Icons.play_arrow,
            color: Colors.white,
            size: 48,
          ),
        ),
      ],
    );
  }
}
