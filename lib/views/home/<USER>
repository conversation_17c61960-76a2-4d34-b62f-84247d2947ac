import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:ui';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:provider/provider.dart';
import 'package:mbokatour/config/app_config.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/models/category.dart';
import 'package:mbokatour/providers/category_provider.dart';
import 'package:heroicons/heroicons.dart';

import 'package:mbokatour/views/detail/place_detail_screen.dart';
import 'package:mbokatour/views/map/openstreetmap_screen.dart';
import 'package:mbokatour/views/home/<USER>';
import 'package:mbokatour/views/home/<USER>';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:mbokatour/controllers/place_controller.dart';
import 'package:mbokatour/controllers/category_controller.dart';
import 'package:mbokatour/repositories/place_repository.dart';
import 'package:mbokatour/repositories/category_repository.dart';
import 'package:mbokatour/services/api_service.dart';
import 'package:mbokatour/services/auth_service.dart';
import 'package:mbokatour/services/video_cache_service.dart';
import 'package:mbokatour/views/onboarding/onboarding_screen.dart';
import 'package:mbokatour/views/profile/profile_screen.dart';
import 'package:mbokatour/views/auth/login_screen.dart';
import 'package:mbokatour/widgets/simple_auth_dialog.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  // Pour l'animation de feedback like/favori
  final Map<String, bool> _likedAnim = {};
  final Map<String, bool> _favAnim = {};
  final CardSwiperController controller = CardSwiperController();

  // Track viewed places to avoid duplicate view increments
  final Set<String> _viewedPlaces = {};
  late final PlaceController _placeController;
  late final CategoryController _categoryController;
  late Map<String, VideoPlayerController> _videoControllers;
  final VideoCacheService _videoCacheService = VideoCacheService();
  final Map<String, bool> _videoInitialized = {};
  final Map<String, bool> _videoErrors = {};
  int? _currentCardIndex = 0;
  bool _showMap = false;
  bool _isLoading = false;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int selectedCategoryId = 0;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  // Données chargées depuis l'API
  List<Place> places = [];
  List<Category> categories = [];

  List<Place> get filteredPlaces {
    if (selectedCategoryId == 0) return places;
    return places.where((place) {
      if (place.categories == null) return false;
      return place.categories!.any((cat) => cat.id == selectedCategoryId);
    }).toList();
  }

  List<Category> get allCategories {
    if (categories.isEmpty) {
      // Catégories par défaut si l'API n'est pas encore chargée
      return [
        Category(id: 0, name: 'Tous'),
        Category(id: 1, name: 'Monuments'),
        Category(id: 2, name: 'Musées'),
        Category(id: 3, name: 'Parcs'),
        Category(id: 4, name: 'Restaurants'),
        Category(id: 5, name: 'Shopping'),
      ];
    }
    return [Category(id: 0, name: 'Tous'), ...categories];
  }

  // Méthode pour adapter la taille de police selon la longueur du texte
  double _getAdaptiveFontSize(String text) {
    if (text.length <= 15) {
      return 20.0; // Taille normale pour texte court
    } else if (text.length <= 25) {
      return 18.0; // Taille réduite pour texte moyen
    } else if (text.length <= 35) {
      return 16.0; // Taille plus réduite pour texte long
    } else {
      return 14.0; // Taille minimale pour texte très long
    }
  }

  @override
  void initState() {
    super.initState();
    _placeController =
        PlaceController(PlaceRepository(ApiService.instance.dio));
    _categoryController =
        CategoryController(CategoryRepository(ApiService.instance.dio));
    WidgetsBinding.instance.addObserver(this);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
    _videoControllers = {};

    // Charger les données depuis l'API
    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      debugPrint('🔄 Début du chargement des données...');

      // Charger les données en parallèle
      final results = await Future.wait([
        _placeController.getDiscover(),
        _categoryController.getCategories().catchError((e) {
          // Si les catégories échouent, retourner une liste vide
          debugPrint('Erreur lors du chargement des catégories: $e');
          return <Category>[];
        }),
      ]);

      final loadedPlaces = results[0] as List<Place>;
      final loadedCategories = results[1] as List<Category>;

      debugPrint('✅ Données reçues:');
      debugPrint('   - Places: ${loadedPlaces.length}');
      debugPrint('   - Categories: ${loadedCategories.length}');

      if (loadedPlaces.isNotEmpty) {
        debugPrint('   - Premier lieu: ${loadedPlaces.first.name}');
        debugPrint('   - ID: ${loadedPlaces.first.id}');
        debugPrint('   - Image: ${loadedPlaces.first.mainImageUrl}');
      }

      if (mounted) {
        // Mettre à jour le Provider avec les données chargées
        final categoryProvider =
            Provider.of<CategoryProvider>(context, listen: false);
        categoryProvider.setCategories(loadedCategories);
        categoryProvider.setPlaces(loadedPlaces);

        setState(() {
          places = loadedPlaces;
          categories = loadedCategories;
          _isLoading = false;
        });

        debugPrint('🎯 État mis à jour - places.length: ${places.length}');
        debugPrint('🎯 filteredPlaces.length: ${filteredPlaces.length}');

        // Initialiser les contrôleurs vidéo après le chargement des données
        _initializeVideoControllers();

        // Précharge les vidéos en arrière-plan
        _preloadVideosInBackground();
      }
    } catch (e) {
      debugPrint('❌ Erreur dans _loadInitialData: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: ${e.toString()}'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Réessayer',
              onPressed: _loadInitialData,
            ),
          ),
        );
      }
    }
  }

  void _initializeVideoControllers() {
    // Initialise seulement les vidéos visibles et les suivantes
    _initializeVideoForIndex(_currentCardIndex ?? 0);
    _preloadAdjacentVideos(_currentCardIndex ?? 0);
  }

  Future<void> _initializeVideoForIndex(int index) async {
    if (index < 0 || index >= places.length) return;

    final place = places[index];
    if (place.mediaType == 'video' &&
        place.mainImageUrl != null &&
        !_videoControllers.containsKey(place.id)) {
      debugPrint('🎬 Initialisation vidéo pour: ${place.name}');

      _videoInitialized[place.id] = false;
      _videoErrors[place.id] = false;

      try {
        // Utilise le service de cache pour obtenir le contrôleur optimisé
        final controller = await _videoCacheService.getVideoController(
          place.mainImageUrl!,
          place.id,
        );

        if (controller == null) {
          throw Exception('Impossible de créer le contrôleur vidéo');
        }

        _videoControllers[place.id] = controller;

        await controller.initialize();

        if (mounted) {
          setState(() {
            _videoInitialized[place.id] = true;
          });

          // Configure le contrôleur
          controller.setLooping(true);
          controller.setVolume(0.5); // Volume à 50%

          // Informations de debug sur la vidéo
          final videoValue = controller.value;
          debugPrint('📹 Vidéo initialisée: ${place.name}');
          debugPrint(
              '   - Taille: ${videoValue.size.width}x${videoValue.size.height}');
          debugPrint('   - Aspect ratio: ${videoValue.aspectRatio}');
          debugPrint('   - Durée: ${videoValue.duration}');
          debugPrint('   - Est initialisée: ${videoValue.isInitialized}');

          // Lance la vidéo si c'est la carte actuelle
          if (index == _currentCardIndex) {
            controller.play();
            debugPrint('▶️ Lecture automatique démarrée pour: ${place.name}');
          }
        }
      } catch (error) {
        if (mounted) {
          setState(() {
            _videoErrors[place.id] = true;
            _videoInitialized[place.id] = false;
          });
          debugPrint('❌ Erreur initialisation vidéo ${place.name}: $error');
        }
      }
    }
  }

  void _preloadAdjacentVideos(int currentIndex) {
    // Précharge la vidéo suivante et précédente
    final nextIndex = (currentIndex + 1) % places.length;
    final prevIndex = (currentIndex - 1 + places.length) % places.length;

    Future.delayed(const Duration(milliseconds: 500), () {
      _initializeVideoForIndex(nextIndex);
    });

    Future.delayed(const Duration(milliseconds: 1000), () {
      _initializeVideoForIndex(prevIndex);
    });
  }

  Future<void> _preloadVideosInBackground() async {
    // Précharge les vidéos en arrière-plan pour améliorer les performances
    final videoPlaces = places
        .where(
            (place) => place.mediaType == 'video' && place.mainImageUrl != null)
        .take(5) // Limite à 5 vidéos pour éviter la surcharge
        .toList();

    if (videoPlaces.isNotEmpty) {
      final videosToPreload = videoPlaces
          .map((place) => {
                'url': place.mainImageUrl!,
                'placeId': place.id,
              })
          .toList();

      // Précharge en arrière-plan sans bloquer l'UI
      _videoCacheService.preloadVideoBatch(videosToPreload);
      debugPrint(
          '🎬 Préchargement de ${videosToPreload.length} vidéos en arrière-plan');
    }
  }

  void _disposeUnusedVideoControllers(int currentIndex) {
    // Dispose les contrôleurs trop éloignés pour économiser la mémoire
    final keepIndices = <int>{};
    for (int i = -2; i <= 2; i++) {
      final index = (currentIndex + i) % places.length;
      if (index >= 0 && index < places.length) {
        keepIndices.add(index);
      }
    }

    final toRemove = <String>[];
    for (final entry in _videoControllers.entries) {
      final placeId = entry.key;
      final placeIndex = places.indexWhere((p) => p.id == placeId);

      if (placeIndex == -1 || !keepIndices.contains(placeIndex)) {
        toRemove.add(placeId);
      }
    }

    for (final placeId in toRemove) {
      _videoControllers[placeId]?.dispose();
      _videoControllers.remove(placeId);
      _videoInitialized.remove(placeId);
      _videoErrors.remove(placeId);
      debugPrint('🗑️ Contrôleur vidéo disposé: $placeId');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _animationController.dispose();
    for (var controller in _videoControllers.values) {
      controller.dispose();
    }
    // Nettoie le service de cache
    _videoCacheService.disposeAll();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        // Pause toutes les vidéos quand l'app passe en arrière-plan
        for (var controller in _videoControllers.values) {
          if (controller.value.isPlaying) {
            controller.pause();
          }
        }
        break;
      case AppLifecycleState.resumed:
        // Reprend la vidéo actuelle quand l'app revient au premier plan
        if (_currentCardIndex != null && _currentCardIndex! < places.length) {
          final currentPlace = places[_currentCardIndex!];
          if (currentPlace.mediaType == 'video' &&
              _videoInitialized[currentPlace.id] == true) {
            final controller = _videoControllers[currentPlace.id];
            if (controller != null && !controller.value.isPlaying) {
              controller.play();
            }
          }
        }
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // Dispose tous les contrôleurs pour libérer la mémoire
        for (var controller in _videoControllers.values) {
          controller.dispose();
        }
        _videoControllers.clear();
        _videoInitialized.clear();
        _videoErrors.clear();
        break;
    }
  }

  Future<void> _refreshData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Recharger les données depuis l'API
      final newPlaces = await _placeController.getDiscover();

      if (mounted) {
        setState(() {
          places = newPlaces;
          _isLoading = false;
        });

        // Réinitialiser les contrôleurs vidéo avec les nouvelles données
        for (var controller in _videoControllers.values) {
          controller.dispose();
        }
        _videoControllers.clear();
        _videoInitialized.clear();
        _videoErrors.clear();
        _initializeVideoControllers();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Données actualisées !'),
            duration: Duration(milliseconds: 1000),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'actualisation: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: Duration(milliseconds: 2000),
            action: SnackBarAction(
              label: 'Réessayer',
              onPressed: _refreshData,
            ),
          ),
        );
      }
    }
  }

  // Handle like toggle with proper API integration
  Future<void> _handleLikeToggle(Place place,
      {bool showSnackBar = false}) async {
    // Vérifier l'authentification avec le widget approprié
    if (!await context.checkAuthAndShowDialog(
      isAuthenticated: AuthService.instance.isAuthenticated,
      title: 'Connexion requise',
      message: 'Vous devez être connecté pour liker des lieux',
      onLoginSuccess: () {
        // Réessayer l'action après connexion
        _handleLikeToggle(place, showSnackBar: showSnackBar);
      },
    )) {
      return;
    }

    // Optimistic UI update
    setState(() {
      _likedAnim[place.id] = true;
    });

    try {
      final result = await _placeController.toggleLike(place.id);
      final isLiked = result['is_liked'] ?? false;
      final likesCount = result['likes_count'] ?? 0;

      // Update the place in the list
      final placeIndex = places.indexWhere((p) => p.id == place.id);
      if (placeIndex != -1) {
        places[placeIndex] = Place(
          id: place.id,
          name: place.name,
          description: place.description,
          location: place.location,
          price: place.price,
          isFree: place.isFree,
          latitude: place.latitude,
          longitude: place.longitude,
          mainImageUrl: place.mainImageUrl,
          mediaType: place.mediaType,
          address: place.address,
          neighborhood: place.neighborhood,
          city: place.city,
          openingHours: place.openingHours,
          status: place.status,
          isActive: place.isActive,
          isFeatured: place.isFeatured,
          viewsCount: place.viewsCount,
          priority: place.priority,
          isLiked: isLiked,
          isFavorited: place.isFavorited,
          likesCount: likesCount,
          categories: place.categories,
          images: place.images,
        );
      }

      if (showSnackBar && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isLiked
                ? '${place.name} ajouté aux likes !'
                : '${place.name} retiré des likes !'),
            duration: const Duration(milliseconds: 1000),
            behavior: SnackBarBehavior.floating,
            backgroundColor: isLiked ? Colors.green : Colors.grey,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (error) {
      debugPrint('Erreur lors du toggle like: $error');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du like: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Reset animation
      Future.delayed(const Duration(milliseconds: 400), () {
        if (mounted) {
          setState(() {
            _likedAnim[place.id] = false;
          });
        }
      });
    }
  }

  // Handle favorite toggle with proper API integration
  Future<void> _handleFavoriteToggle(Place place,
      {bool showSnackBar = false}) async {
    // Vérifier l'authentification avec le widget approprié
    if (!await context.checkAuthAndShowDialog(
      isAuthenticated: AuthService.instance.isAuthenticated,
      title: 'Connexion requise',
      message: 'Vous devez être connecté pour ajouter aux favoris',
      onLoginSuccess: () {
        // Réessayer l'action après connexion
        _handleFavoriteToggle(place, showSnackBar: showSnackBar);
      },
    )) {
      return;
    }

    // Optimistic UI update
    setState(() {
      _favAnim[place.id] = true;
    });

    try {
      final isFavorited = place.isFavorited ?? false;

      if (isFavorited) {
        await _placeController.removeFromFavorite(place.id);
      } else {
        await _placeController.addToFavorite(place.id);
      }

      // Update the place in the list
      final placeIndex = places.indexWhere((p) => p.id == place.id);
      if (placeIndex != -1) {
        places[placeIndex] = Place(
          id: place.id,
          name: place.name,
          description: place.description,
          location: place.location,
          price: place.price,
          isFree: place.isFree,
          latitude: place.latitude,
          longitude: place.longitude,
          mainImageUrl: place.mainImageUrl,
          mediaType: place.mediaType,
          address: place.address,
          neighborhood: place.neighborhood,
          city: place.city,
          openingHours: place.openingHours,
          status: place.status,
          isActive: place.isActive,
          isFeatured: place.isFeatured,
          viewsCount: place.viewsCount,
          priority: place.priority,
          isLiked: place.isLiked,
          isFavorited: !isFavorited,
          likesCount: place.likesCount,
          categories: place.categories,
          images: place.images,
        );
      }

      if (showSnackBar && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(!isFavorited
                ? '${place.name} ajouté aux favoris !'
                : '${place.name} retiré des favoris !'),
            duration: const Duration(milliseconds: 1000),
            behavior: SnackBarBehavior.floating,
            backgroundColor: !isFavorited ? Colors.red : Colors.grey,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (error) {
      debugPrint('Erreur lors du toggle favori: $error');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Erreur lors de l\'ajout aux favoris: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Reset animation
      Future.delayed(const Duration(milliseconds: 400), () {
        if (mounted) {
          setState(() {
            _favAnim[place.id] = false;
          });
        }
      });
    }
  }

  Future<void> _handleLogout() async {
    // Afficher une boîte de dialogue de confirmation
    final bool? shouldLogout = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Déconnexion'),
          content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Déconnexion'),
            ),
          ],
        );
      },
    );

    if (shouldLogout == true) {
      try {
        // Déconnecter l'utilisateur
        await AuthService.instance.clearAuth();

        if (mounted) {
          // Naviguer vers l'écran d'onboarding
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => const OnboardingScreen()),
            (route) => false,
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la déconnexion: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppConfig.primary[500]!,
              AppConfig.primary[400]!,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _refreshData,
            child: Stack(
              children: [
                Column(
                  children: [
                    _buildHeader(),
                    const SizedBox(height: 8),
                    _buildCategories(),
                    const SizedBox(height: 16),
                    Expanded(
                      child: Stack(
                        children: [
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            transitionBuilder:
                                (Widget child, Animation<double> animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: SlideTransition(
                                  position: Tween<Offset>(
                                    begin: const Offset(0.0, 0.1),
                                    end: Offset.zero,
                                  ).animate(animation),
                                  child: child,
                                ),
                              );
                            },
                            child: _showMap
                                ? const OpenStreetMapScreen(
                                    key: ValueKey('map'),
                                  )
                                : SizedBox(
                                    key: const ValueKey('cards'),
                                    width: double.infinity,
                                    height: double.infinity,
                                    child: _buildCardSwiper(),
                                  ),
                          ),
                          if (_isLoading) _buildLoadingOverlay(),
                        ],
                      ),
                    ),
                  ],
                ),
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: _buildButtons(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCardSwiper() {
    debugPrint('🎨 _buildCardSwiper appelé:');
    debugPrint('   - _isLoading: $_isLoading');
    debugPrint('   - places.length: ${places.length}');
    debugPrint('   - filteredPlaces.length: ${filteredPlaces.length}');
    debugPrint('   - selectedCategoryId: $selectedCategoryId');

    if (_isLoading && places.isEmpty) {
      debugPrint('🔄 Affichage du loader');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppConfig.primary[600]),
            SizedBox(height: 16),
            Text(
              "Chargement des lieux depuis l'API...",
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (filteredPlaces.isEmpty) {
      debugPrint('📭 Affichage du message vide');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info_outline, size: 48, color: AppConfig.primary[900]),
            SizedBox(height: 16),
            Text(
              places.isEmpty
                  ? "Aucun lieu disponible.\nVérifiez votre connexion et tirez pour actualiser."
                  : "Aucun lieu à afficher pour cette catégorie.",
              style: TextStyle(fontSize: 18, color: AppConfig.primary[900]),
              textAlign: TextAlign.center,
            ),
            if (places.isEmpty) ...[
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadInitialData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConfig.primary[800],
                ),
                child: Text('Réessayer', style: TextStyle(color: Colors.white)),
              ),
            ],
          ],
        ),
      );
    }

    debugPrint(
        '🎯 Affichage du CardSwiper avec ${filteredPlaces.length} places');
    return CardSwiper(
      controller: controller,
      cardsCount: filteredPlaces.length,
      onSwipe: _onSwipe,
      onUndo: _onUndo,
      isLoop: true,
      numberOfCardsDisplayed: filteredPlaces.isNotEmpty ? 1 : 0,
      backCardOffset: const Offset(0, 20),
      padding: EdgeInsets.zero,
      cardBuilder: (context, index, horizontalOffsetPercentage,
          verticalOffsetPercentage) {
        if (index >= filteredPlaces.length) return null;

        final place = filteredPlaces[index];

        // Incrémenter les vues automatiquement quand la carte apparaît
        if (!_viewedPlaces.contains(place.id)) {
          _viewedPlaces.add(place.id);
          // Appeler findPlaceById pour incrémenter les vues (l'API le fait automatiquement)
          _placeController.findPlaceById(place.id).then((_) {
            debugPrint('📈 Vues incrémentées pour: ${place.name}');
          }).catchError((e) {
            debugPrint('Erreur lors de l\'incrémentation des vues: $e');
          });
        }

        return _buildPlaceCard(place);
      },
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.0, -0.5),
          end: Offset.zero,
        ).animate(_animationController),
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  if (AuthService.instance.isAuthenticated) {
                    // Si connecté, aller au profil
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProfileScreen(),
                      ),
                    );
                  } else {
                    // Si non connecté, aller à la connexion
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LoginScreen(),
                      ),
                    );
                  }
                },
                child: Container(
                  padding: EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: Image.asset(
                            'assets/logos/logo.png',
                            width: 40,
                            height: 40,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AuthService.instance.currentUser?.name ??
                                'MbokaTour',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontWeight: FontWeight.w800,
                                ),
                          ),
                          Text(
                            'Découvrez de nouveaux lieux',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: IconButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SearchScreen(
                              allPlaces: places,
                              categories: allCategories,
                            ),
                          ),
                        );
                      },
                      icon: HeroIcon(
                        HeroIcons.magnifyingGlass,
                        style: HeroIconStyle.outline,
                        color: Colors.black,
                        size: 20,
                      ),
                      tooltip: 'Rechercher',
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: IconButton(
                      onPressed: () async {
                        // Vérifier l'authentification avant d'accéder aux favoris
                        if (!await context.checkAuthAndShowDialog(
                          isAuthenticated: AuthService.instance.isAuthenticated,
                          title: 'Connexion requise',
                          message:
                              'Vous devez être connecté pour voir vos favoris',
                          onLoginSuccess: () {
                            // Naviguer vers les favoris après connexion
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const FavoritesScreen(),
                              ),
                            );
                          },
                        )) {
                          return;
                        }

                        // Si déjà connecté, naviguer directement
                        if (mounted) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const FavoritesScreen(),
                            ),
                          );
                        }
                      },
                      icon: HeroIcon(
                        HeroIcons.heart,
                        style: HeroIconStyle.outline,
                        color: Colors.red,
                        size: 20,
                      ),
                      tooltip: 'Favoris',
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Bouton de déconnexion (seulement si connecté)
                  if (AuthService.instance.isAuthenticated)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.red[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: _handleLogout,
                        icon: HeroIcon(
                          HeroIcons.arrowRightOnRectangle,
                          style: HeroIconStyle.outline,
                          color: Colors.red,
                          size: 20,
                        ),
                        tooltip: 'Déconnexion',
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategories() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(-0.2, 0.0),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: allCategories.map((category) {
                final bool isSelected = selectedCategoryId == category.id;
                return Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: GestureDetector(
                    onTap: () {
                      // Mettre à jour la catégorie sélectionnée dans le Provider
                      Provider.of<CategoryProvider>(context, listen: false)
                          .setSelectedCategory(category.id);
                      setState(() {
                        selectedCategoryId = category.id;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 8.0,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.white : Colors.transparent,
                        borderRadius: BorderRadius.circular(18),
                      ),
                      child: Text(
                        category.name,
                        style: TextStyle(
                          color: isSelected ? Colors.black : Colors.black,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceCard(Place place) {
    final GlobalKey<_PlaceCardContentState> contentKey =
        GlobalKey<_PlaceCardContentState>();

    // Variables pour détecter les swipes horizontaux et verticaux
    double startX = 0.0;
    double currentX = 0.0;
    double startY = 0.0;
    double currentY = 0.0;
    final double swipeThreshold =
        50.0; // Distance minimale pour considérer un swipe

    return GestureDetector(
      onTap: () {
        // contentKey.currentState?.nextImage();

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlaceDetailScreen(
              place: place,
              videoController: _videoControllers[place.id],
            ),
          ),
        );
      },
      onDoubleTap: () => _handleFavoriteToggle(place),
      onPanStart: (details) {
        startX = details.localPosition.dx;
        startY = details.localPosition.dy;
      },
      onPanUpdate: (details) {
        currentX = details.localPosition.dx;
        currentY = details.localPosition.dy;
      },
      onPanEnd: (details) {
        final double deltaX = currentX - startX;
        final double deltaY = currentY - startY;

        // Détermine si le swipe est plus horizontal ou vertical
        if (deltaX.abs() > deltaY.abs()) {
          // Swipe horizontal - navigation entre images
          if (deltaX > swipeThreshold) {
            contentKey.currentState?.previousImage();
          } else if (deltaX < -swipeThreshold) {
            contentKey.currentState?.nextImage();
          }
        } else {
          // Swipe vertical - navigation entre cartes
          if (deltaY > swipeThreshold) {
            // Swipe vers le bas - carte précédente
            controller.undo();
          } else if (deltaY < -swipeThreshold) {
            // Swipe vers le haut - carte suivante
            controller.swipe(CardSwiperDirection.bottom);
          }
        }
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Stack(
          fit: StackFit.expand, // Force le Stack à prendre tout l'espace
          children: [
            _PlaceCardContent(
              key: contentKey,
              place: place,
              videoController: _videoControllers[place.id],
              isVideoInitialized: _videoInitialized[place.id],
              hasVideoError: _videoErrors[place.id],
            ),
            _buildCardInfoWithButtons(place),
          ],
        ),
      ),
    );
  }

  Widget _buildCardInfoWithButtons(Place place) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.4),
              Colors.black.withValues(alpha: 0.8),
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(10.0, 10.0, 10.0, 80.0),
              child: IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Card Info - takes most of the space with tap gestures
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          // Tap simple pour voir les détails
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PlaceDetailScreen(
                                place: place,
                                videoController: _videoControllers[place.id],
                              ),
                            ),
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 16,
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      place.name,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize:
                                            _getAdaptiveFontSize(place.name),
                                        fontWeight: FontWeight.bold,
                                        shadows: const [
                                          Shadow(
                                            color: Colors.black87,
                                            offset: Offset(0, 2),
                                            blurRadius: 6,
                                          ),
                                          Shadow(
                                            color: Colors.black54,
                                            offset: Offset(1, 1),
                                            blurRadius: 3,
                                          ),
                                          Shadow(
                                            color: Colors.black26,
                                            offset: Offset(0, 4),
                                            blurRadius: 8,
                                          ),
                                        ],
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  HeroIcon(
                                    HeroIcons.mapPin,
                                    style: HeroIconStyle.solid,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      place.location ??
                                          'Localisation non disponible',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                        shadows: [
                                          Shadow(
                                            color: Colors.black87,
                                            offset: Offset(0, 1),
                                            blurRadius: 4,
                                          ),
                                          Shadow(
                                            color: Colors.black54,
                                            offset: Offset(1, 1),
                                            blurRadius: 2,
                                          ),
                                          Shadow(
                                            color: Colors.black26,
                                            offset: Offset(0, 3),
                                            blurRadius: 6,
                                          ),
                                        ],
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  // Affichage des vues
                                  HeroIcon(
                                    HeroIcons.eye,
                                    style: HeroIconStyle.outline,
                                    color: Colors.white70,
                                    size: 14,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    '${place.viewsCount ?? 0}',
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black87,
                                          offset: Offset(0, 1),
                                          blurRadius: 3,
                                        ),
                                        Shadow(
                                          color: Colors.black54,
                                          offset: Offset(1, 1),
                                          blurRadius: 2,
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  // Affichage des likes
                                  HeroIcon(
                                    HeroIcons.handThumbUp,
                                    style: HeroIconStyle.outline,
                                    color: Colors.white70,
                                    size: 14,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    '${place.likesCount ?? 0}',
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black87,
                                          offset: Offset(0, 1),
                                          blurRadius: 3,
                                        ),
                                        Shadow(
                                          color: Colors.black54,
                                          offset: Offset(1, 1),
                                          blurRadius: 2,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Action Buttons - seulement like et favorite
                    Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        AnimatedScale(
                          scale: _favAnim[place.id] == true ? 1.4 : 1.0,
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeOut,
                          child: IconButton(
                            icon: HeroIcon(
                              (_favAnim[place.id] == true ||
                                      place.isFavorited == true)
                                  ? HeroIcons.heart
                                  : HeroIcons.heart,
                              style: (_favAnim[place.id] == true ||
                                      place.isFavorited == true)
                                  ? HeroIconStyle.solid
                                  : HeroIconStyle.outline,
                              color: (_favAnim[place.id] == true ||
                                      place.isFavorited == true)
                                  ? Colors.redAccent
                                  : Colors.white,
                              size: 30,
                            ),
                            onPressed: () => _handleFavoriteToggle(place),
                          ),
                        ),
                        AnimatedScale(
                          scale: _likedAnim[place.id] == true ? 1.4 : 1.0,
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeOut,
                          child: IconButton(
                            icon: HeroIcon(
                              (_likedAnim[place.id] == true ||
                                      place.isLiked == true)
                                  ? HeroIcons.handThumbUp
                                  : HeroIcons.handThumbUp,
                              style: (_likedAnim[place.id] == true ||
                                      place.isLiked == true)
                                  ? HeroIconStyle.solid
                                  : HeroIconStyle.outline,
                              color: (_likedAnim[place.id] == true ||
                                      place.isLiked == true)
                                  ? Colors.blueAccent
                                  : Colors.white,
                              size: 30,
                            ),
                            onPressed: () => _handleLikeToggle(place),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButtons() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0.0, 0.5),
          end: Offset.zero,
        ).animate(_animationController),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        setState(() {
                          _showMap = false;
                        });
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 6),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          HeroIcon(
                            HeroIcons.sparkles,
                            style: HeroIconStyle.outline,
                            color: _showMap
                                ? Colors.black54
                                : AppConfig.primary[600],
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Découvrir',
                            style: TextStyle(
                              color: _showMap
                                  ? Colors.black54
                                  : AppConfig.primary[600],
                              fontWeight: _showMap
                                  ? FontWeight.normal
                                  : FontWeight.bold,
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        setState(() {
                          _showMap = true;
                        });
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 6),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          HeroIcon(
                            HeroIcons.mapPin,
                            style: HeroIconStyle.outline,
                            color: _showMap
                                ? AppConfig.primary[600]
                                : Colors.black54,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Je m\'ennuie',
                            style: TextStyle(
                              color: _showMap
                                  ? AppConfig.primary[600]
                                  : Colors.black54,
                              fontWeight: _showMap
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool _onSwipe(
    int previousIndex,
    int? currentIndex,
    CardSwiperDirection direction,
  ) {
    setState(() {
      _currentCardIndex = currentIndex;
    });

    // Pause la vidéo précédente
    final previousPlace = places[previousIndex];
    if (previousPlace.mediaType == 'video') {
      _videoControllers[previousPlace.id]?.pause();
    }

    // Lance la vidéo actuelle et précharge les suivantes
    if (currentIndex != null) {
      final currentPlace = places[currentIndex];

      // Initialise la vidéo actuelle si nécessaire
      _initializeVideoForIndex(currentIndex);

      // Lance la vidéo si elle est prête
      if (currentPlace.mediaType == 'video' &&
          _videoInitialized[currentPlace.id] == true) {
        _videoControllers[currentPlace.id]?.play();
      }

      // Précharge les vidéos adjacentes
      _preloadAdjacentVideos(currentIndex);

      // Nettoie les contrôleurs inutiles
      _disposeUnusedVideoControllers(currentIndex);
    }

    // Actions automatiques selon la direction
    if (direction == CardSwiperDirection.right) {
      // Auto-like quand on swipe à droite
      _handleLikeToggle(previousPlace, showSnackBar: true);
    } else if (direction == CardSwiperDirection.top) {
      // Auto-favori quand on swipe vers le haut
      _handleFavoriteToggle(previousPlace, showSnackBar: true);
    }

    debugPrint(
      'The card $previousIndex was swiped to the ${direction.name}. Now the card $currentIndex is on top',
    );
    return true;
  }

  bool _onUndo(
    int? previousIndex,
    int currentIndex,
    CardSwiperDirection direction,
  ) {
    setState(() {
      _currentCardIndex = currentIndex;
    });

    // Pause la vidéo précédente
    if (previousIndex != null) {
      final previousPlace = places[previousIndex];
      if (previousPlace.mediaType == 'video') {
        _videoControllers[previousPlace.id]?.pause();
      }
    }

    // Gère la vidéo actuelle
    final currentPlace = places[currentIndex];

    // Initialise la vidéo actuelle si nécessaire
    _initializeVideoForIndex(currentIndex);

    // Lance la vidéo si elle est prête
    if (currentPlace.mediaType == 'video' &&
        _videoInitialized[currentPlace.id] == true) {
      _videoControllers[currentPlace.id]?.play();
    }

    // Précharge les vidéos adjacentes
    _preloadAdjacentVideos(currentIndex);

    debugPrint(
      'The card $currentIndex was undod from the ${direction.name}',
    );
    return true;
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.3),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Actualisation en cours...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PlaceCardContent extends StatefulWidget {
  final Place place;
  final VideoPlayerController? videoController;
  final bool? isVideoInitialized;
  final bool? hasVideoError;

  const _PlaceCardContent({
    super.key,
    required this.place,
    this.videoController,
    this.isVideoInitialized,
    this.hasVideoError,
  });

  @override
  State<_PlaceCardContent> createState() => _PlaceCardContentState();
}

class _PlaceCardContentState extends State<_PlaceCardContent> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController.addListener(() {
      final imageUrls = allImageUrls;
      final nextIndex = (_pageController.page!.round() + 1) % imageUrls.length;
      final prevIndex = (_pageController.page!.round() - 1 + imageUrls.length) %
          imageUrls.length;
      // Précharge uniquement l'image suivante et précédente
      if (imageUrls.isNotEmpty) {
        precacheImage(
            CachedNetworkImageProvider(imageUrls[nextIndex]), context);
        precacheImage(
            CachedNetworkImageProvider(imageUrls[prevIndex]), context);
      }
      setState(() {
        _currentPage = _pageController.page!.round();
      });
    });
    // Précharge initiale : seulement l'image suivante/précédente si plusieurs images
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final imageUrls = allImageUrls;
      if (imageUrls.length > 1) {
        precacheImage(CachedNetworkImageProvider(imageUrls[1]), context);
        precacheImage(CachedNetworkImageProvider(imageUrls.last), context);
      }
    });
  }

  void nextImage() {
    final imageCount = allImageUrls.length;
    if (imageCount > 1) {
      int nextPage = (_currentPage + 1) % imageCount;
      _pageController.animateToPage(
        nextPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void previousImage() {
    final imageCount = allImageUrls.length;
    if (imageCount > 1) {
      int prevPage = (_currentPage - 1 + imageCount) % imageCount;
      _pageController.animateToPage(
        prevPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  List<String> get allImageUrls {
    final urls = <String>[];
    // Only include mainImageUrl if this is an image, not a video
    if (widget.place.mediaType != 'video' &&
        widget.place.mainImageUrl != null) {
      urls.add(widget.place.mainImageUrl!);
    }
    if (widget.place.images != null) {
      urls.addAll(widget.place.images!.map((img) => img.imageUrl));
    }
    return urls;
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // Méthode inspirée de place_detail_screen.dart pour afficher les vidéos
  Widget _buildVideoPlayerForCard() {
    final controller = widget.videoController;
    final hasVideoError = widget.hasVideoError == true;
    final isVideoInitialized = widget.isVideoInitialized == true;

    debugPrint('🎬 _buildVideoPlayerForCard pour: ${widget.place.name}');
    debugPrint('   - controller: ${controller != null}');
    debugPrint('   - isVideoInitialized: $isVideoInitialized');
    debugPrint('   - hasVideoError: $hasVideoError');
    debugPrint(
        '   - controller.value.isInitialized: ${controller?.value.isInitialized}');

    // Si il y a une erreur, afficher une image de fallback
    if (hasVideoError) {
      return _buildVideoFallback();
    }

    // Si la vidéo est initialisée, l'afficher (même logique que place_detail_screen.dart)
    if (controller != null &&
        isVideoInitialized &&
        controller.value.isInitialized) {
      debugPrint('✅ Affichage vidéo réussie pour: ${widget.place.name}');
      debugPrint(
          '   - Taille vidéo: ${controller.value.size.width}x${controller.value.size.height}');
      debugPrint('   - Aspect ratio: ${controller.value.aspectRatio}');

      return Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: FittedBox(
              fit: BoxFit.cover, // Même que place_detail_screen.dart
              child: SizedBox(
                width: controller.value.size.width,
                height: controller.value.size.height,
                child: VideoPlayer(controller),
              ),
            ),
          ),
          // Overlay avec contrôles (même que place_detail_screen.dart)
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.3),
                ],
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 48,
            ),
            onPressed: () {
              setState(() {
                if (controller.value.isPlaying) {
                  controller.pause();
                } else {
                  controller.play();
                }
              });
            },
          ),
          // Indicateur de chargement si la vidéo est en cours de buffering
          if (controller.value.isBuffering)
            const Positioned(
              bottom: 20,
              right: 20,
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      );
    }

    // Afficher un indicateur de chargement (même que place_detail_screen.dart)
    debugPrint('⏳ Affichage loader pour: ${widget.place.name}');
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[200],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Chargement de la vidéo...',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoFallback() {
    // Fallback en cas d'erreur (inspiré de place_detail_screen.dart)
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Essayer d'afficher une image de fallback depuis les autres images du lieu
          if (widget.place.images != null && widget.place.images!.isNotEmpty)
            CachedNetworkImage(
              imageUrl: widget.place.images!.first.imageUrl,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorWidget: (context, url, error) =>
                  _buildDefaultVideoFallback(),
            )
          else
            _buildDefaultVideoFallback(),
          // Overlay indiquant que c'est une vidéo
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.play_circle_outline,
                size: 64,
                color: Colors.white,
              ),
              const SizedBox(height: 8),
              const Text(
                'Vidéo non disponible',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultVideoFallback() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: const Icon(
        Icons.videocam_off,
        size: 48,
        color: Colors.grey,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final imageUrls = allImageUrls;

    // Détermine si on doit afficher la vidéo
    final isVideoPlace = widget.place.mediaType == 'video';
    final hasVideoController = widget.videoController != null;
    final isVideoReady = widget.isVideoInitialized == true &&
        hasVideoController &&
        widget.videoController!.value.isInitialized;
    final hasVideoError = widget.hasVideoError == true;

    // Compte les pages : vidéo (si prête ou en cours de chargement) + images
    final shouldShowVideoPage = isVideoPlace &&
        (isVideoReady || (!hasVideoError && hasVideoController));
    final pageCount = (shouldShowVideoPage ? 1 : 0) + imageUrls.length;

    // Si c'est une vidéo et qu'il n'y a pas d'images, on affiche quand même la vidéo
    if (imageUrls.isEmpty && !isVideoPlace) {
      return const Center(child: Icon(Icons.image_not_supported));
    }

    // Si c'est une vidéo sans images supplémentaires, on affiche juste la vidéo
    if (imageUrls.isEmpty && isVideoPlace) {
      debugPrint('🎬 Affichage vidéo seule pour: ${widget.place.name}');
      return _buildVideoPlayerForCard();
    }

    return Stack(
      fit: StackFit.expand, // Force le Stack à prendre tout l'espace disponible
      children: [
        PageView.builder(
          physics: const ClampingScrollPhysics(),
          controller: _pageController,
          itemCount: pageCount,
          onPageChanged: (index) {
            setState(() {
              _currentPage = index;
            });
          },
          itemBuilder: (context, index) {
            if (shouldShowVideoPage && index == 0) {
              // Utilise la même logique que place_detail_screen.dart
              return _buildVideoPlayerForCard();
            } else {
              final imgIndex = shouldShowVideoPage ? index - 1 : index;
              // Sécurise l'accès à imageUrls
              if (imgIndex < 0 || imgIndex >= imageUrls.length) {
                return const SizedBox.shrink();
              }
              return SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: CachedNetworkImage(
                  imageUrl: imageUrls[imgIndex],
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  placeholder: (context, url) =>
                      const Center(child: CircularProgressIndicator()),
                  errorWidget: (context, url, error) {
                    debugPrint('Error loading image: $error');
                    return const Center(child: Icon(Icons.error));
                  },
                ),
              );
            }
          },
        ),
        if (pageCount > 1)
          Positioned(
            top: 10,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(pageCount, (index) {
                return Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  padding: EdgeInsets.zero,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentPage == index
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                    ),
                  ),
                );
              }),
            ),
          ),
      ],
    );
  }
}
