import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import 'package:mbokatour/models/place.dart';
import 'package:mbokatour/services/location_service.dart';
import 'package:mbokatour/config/app_config.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

class RouteScreen extends StatefulWidget {
  final Place destination;

  const RouteScreen({
    super.key,
    required this.destination,
  });

  @override
  State<RouteScreen> createState() => _RouteScreenState();
}

class _RouteScreenState extends State<RouteScreen> {
  final MapController _mapController = MapController();
  Position? _currentPosition;
  bool _isLoading = true;
  String? _errorMessage;
  double? _distance;
  String? _estimatedTime;
  StreamSubscription<Position>? _positionStream;
  bool _isNavigating = false;
  List<LatLng> _routePoints = [];
  bool _isLoadingRoute = false;

  // Position par défaut (Kinshasa)
  final LatLng _defaultLocation = const LatLng(-4.4419, 15.2663);

  @override
  void initState() {
    super.initState();
    _loadRouteData();
    _startGPSNavigation();
  }

  @override
  void dispose() {
    _positionStream?.cancel();
    super.dispose();
  }

  Future<void> _loadRouteData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Obtenir la position actuelle
      final locationResult = await LocationService.getLocationWithStatus();

      if (locationResult.isSuccess) {
        _currentPosition = locationResult.position;

        // Calculer la distance
        if (widget.destination.latitude != null &&
            widget.destination.longitude != null) {
          _distance = LocationService.calculateDistance(
            _currentPosition!.latitude,
            _currentPosition!.longitude,
            widget.destination.latitude!,
            widget.destination.longitude!,
          );

          // Estimer le temps de trajet (vitesse moyenne de 30 km/h en ville)
          _estimatedTime = _calculateEstimatedTime(_distance!);
        }
      } else {
        _errorMessage = locationResult.error;
      }
    } catch (e) {
      _errorMessage = 'Erreur lors du chargement: $e';
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _calculateEstimatedTime(double distanceKm) {
    // Vitesse moyenne en ville: 30 km/h
    double timeInHours = distanceKm / 30;
    int minutes = (timeInHours * 60).round();

    if (minutes < 60) {
      return '$minutes min';
    } else {
      int hours = minutes ~/ 60;
      int remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}min';
    }
  }

  // Démarrer la navigation GPS en temps réel
  Future<void> _startGPSNavigation() async {
    try {
      setState(() {
        _isNavigating = true;
      });

      // Vérifier les permissions de localisation
      bool hasPermission = await LocationService.requestLocationPermission();
      if (!hasPermission) {
        throw Exception('Permission de localisation refusée');
      }

      // Démarrer le suivi de position en temps réel
      const LocationSettings locationSettings = LocationSettings(
        accuracy: LocationAccuracy.bestForNavigation,
        distanceFilter: 5, // Mise à jour tous les 5 mètres
      );

      _positionStream = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _updateCurrentPosition(position);
        },
        onError: (error) {
          debugPrint('Erreur de géolocalisation: $error');
          if (mounted) {
            setState(() {
              _errorMessage = 'Erreur de géolocalisation: $error';
            });
          }
        },
      );

      // Navigation GPS intégrée - pas besoin d'ouvrir Google Maps
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la navigation: $e');
      if (mounted) {
        setState(() {
          _isNavigating = false;
          _errorMessage = 'Erreur lors du démarrage de la navigation: $e';
        });
      }
    }
  }

  // Mettre à jour la position actuelle et recalculer la distance
  void _updateCurrentPosition(Position position) {
    if (!mounted) return;

    setState(() {
      _currentPosition = position;

      // Recalculer la distance et le temps estimé
      if (widget.destination.latitude != null &&
          widget.destination.longitude != null) {
        _distance = LocationService.calculateDistance(
          position.latitude,
          position.longitude,
          widget.destination.latitude!,
          widget.destination.longitude!,
        );
        _estimatedTime = _calculateEstimatedTime(_distance!);
      }
    });

    // Récupérer la route réelle
    _fetchRealRoute();

    // Centrer la carte sur la nouvelle position
    _mapController.move(
      LatLng(position.latitude, position.longitude),
      17.0, // Zoom plus proche pour la navigation
    );
  }

  // Arrêter la navigation
  void _stopNavigation() {
    _positionStream?.cancel();
    setState(() {
      _isNavigating = false;
    });
  }

  List<Marker> _buildMarkers() {
    List<Marker> markers = [];

    // Marqueur de la position actuelle
    if (_currentPosition != null) {
      markers.add(
        Marker(
          point:
              LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          width: 40,
          height: 40,
          child: Container(
            decoration: BoxDecoration(
              color: _isNavigating ? Colors.green : Colors.blue,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.4),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Icon(
              _isNavigating ? Icons.navigation : Icons.my_location,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      );
    }

    // Marqueur de la destination
    if (widget.destination.latitude != null &&
        widget.destination.longitude != null) {
      markers.add(
        Marker(
          point: LatLng(
              widget.destination.latitude!, widget.destination.longitude!),
          width: 50,
          height: 50,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.4),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: const Icon(
              Icons.flag,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),
      );
    }

    return markers;
  }

  // Obtenir la route réelle en utilisant OSRM (Open Source Routing Machine)
  Future<void> _fetchRealRoute() async {
    if (_currentPosition == null ||
        widget.destination.latitude == null ||
        widget.destination.longitude == null) {
      return;
    }

    setState(() {
      _isLoadingRoute = true;
    });

    try {
      final startLng = _currentPosition!.longitude;
      final startLat = _currentPosition!.latitude;
      final endLng = widget.destination.longitude!;
      final endLat = widget.destination.latitude!;

      // Utiliser OSRM pour obtenir la route réelle
      final url =
          'https://router.project-osrm.org/route/v1/driving/$startLng,$startLat;$endLng,$endLat?overview=full&geometries=geojson';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['routes'] != null && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final geometry = route['geometry'];

          if (geometry != null && geometry['coordinates'] != null) {
            final coordinates = geometry['coordinates'] as List;

            setState(() {
              _routePoints = coordinates.map<LatLng>((coord) {
                return LatLng(coord[1].toDouble(), coord[0].toDouble());
              }).toList();
            });
          }
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération de la route: $e');
      // En cas d'erreur, utiliser une ligne droite
      setState(() {
        _routePoints = [
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          LatLng(widget.destination.latitude!, widget.destination.longitude!),
        ];
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingRoute = false;
        });
      }
    }
  }

  // Construire la ligne de route avec les points réels
  List<Polyline> _buildRouteLine() {
    if (_routePoints.isEmpty) {
      return [];
    }

    return [
      Polyline(
        points: _routePoints,
        color: _isNavigating ? Colors.blue : AppConfig.primary[500]!,
        strokeWidth: 5.0,
      ),
    ];
  }

  Widget _buildRouteInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppConfig.primary[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.route,
                  color: AppConfig.primary[600],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Itinéraire',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Destination
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: AppConfig.primary[500],
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.destination.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (widget.destination.address != null)
                      Text(
                        widget.destination.address!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          if (_distance != null && _estimatedTime != null) ...[
            const SizedBox(height: 16),
            Divider(color: Colors.grey[200]),
            const SizedBox(height: 16),

            // Informations de trajet
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    icon: Icons.straighten,
                    label: 'Distance',
                    value: LocationService.formatDistance(_distance!),
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoItem(
                    icon: Icons.access_time,
                    label: 'Temps estimé',
                    value: _estimatedTime!,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // Contrôles flottants en haut (bouton retour et statut)
  Widget _buildTopControls() {
    return Row(
      children: [
        // Bouton retour
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        const SizedBox(width: 12),
        // Statut de navigation
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: _isNavigating ? Colors.green : Colors.orange,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  _isNavigating ? Icons.navigation : Icons.gps_fixed,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _isNavigating ? 'Navigation active' : 'GPS en cours...',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Informations de navigation flottantes
  Widget _buildNavigationInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Destination
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.destination.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Informations de trajet
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.straighten,
                  label: 'Distance',
                  value: LocationService.formatDistance(_distance!),
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoItem(
                  icon: Icons.access_time,
                  label: 'Temps estimé',
                  value: _estimatedTime!,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Contrôles de navigation en bas
  Widget _buildBottomControls() {
    return Container(
      width: double.infinity,
      child: _isNavigating
          ? Row(
              children: [
                // Bouton centrer sur position
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.my_location, color: Colors.blue),
                    onPressed: () {
                      if (_currentPosition != null) {
                        _mapController.move(
                          LatLng(_currentPosition!.latitude,
                              _currentPosition!.longitude),
                          17.0,
                        );
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),
                // Bouton arrêter navigation
                Expanded(
                  child: ElevatedButton(
                    onPressed: _stopNavigation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 56),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.stop, size: 20),
                        SizedBox(width: 12),
                        Text(
                          'Arrêter la navigation',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )
          : ElevatedButton(
              onPressed: _startGPSNavigation,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 56),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.navigation, size: 20),
                  SizedBox(width: 12),
                  Text(
                    'Démarrer la navigation',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // Carte en plein écran
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: _currentPosition != null
                  ? LatLng(
                      _currentPosition!.latitude, _currentPosition!.longitude)
                  : _defaultLocation,
              initialZoom: 15.0,
              minZoom: 5.0,
              maxZoom: 20.0,
            ),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.mboka_tour_app',
              ),
              PolylineLayer(polylines: _buildRouteLine()),
              MarkerLayer(markers: _buildMarkers()),
            ],
          ),

          // Contrôles flottants en haut
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 16,
            right: 16,
            child: _buildTopControls(),
          ),

          // Informations de navigation flottantes
          if (_distance != null && _estimatedTime != null)
            Positioned(
              top: MediaQuery.of(context).padding.top + 80,
              left: 16,
              right: 16,
              child: _buildNavigationInfo(),
            ),

          // Contrôles de navigation en bas
          Positioned(
            bottom: 30,
            left: 16,
            right: 16,
            child: _buildBottomControls(),
          ),

          // Indicateur de chargement de route
          if (_isLoadingRoute)
            Positioned(
              top: MediaQuery.of(context).padding.top + 150,
              left: 16,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Calcul de l\'itinéraire...',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
