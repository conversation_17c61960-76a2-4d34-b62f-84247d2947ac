import 'package:dio/dio.dart';
import 'package:mbokatour/models/comment.dart';

class CommentRepository {
  final Dio dio;

  CommentRepository(this.dio);

  Future<List<Comment>> getCommentsByPlaceId(String placeId) async {
    try {
      final response = await dio.get('/places/$placeId/comments');
      return (response.data as List)
          .map((json) => Comment.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load comments');
    }
  }

  Future<Comment> addComment({
    required String placeId,
    required String content,
    required double rating,
  }) async {
    try {
      // Debug: Afficher les données envoyées
      final requestData = {
        'content': content,
        'rating': rating,
      };

      print('🔄 Envoi requête POST /places/$placeId/comments');
      print('📤 Données: $requestData');

      final response =
          await dio.post('/places/$placeId/comments', data: requestData);

      print('✅ Réponse reçue: ${response.statusCode}');
      print('📥 Données: ${response.data}');

      // Votre API retourne { "message": "...", "comment": {...} }
      if (response.data is Map && response.data['comment'] != null) {
        return Comment.fromJson(response.data['comment']);
      } else if (response.data is Map && response.data['id'] != null) {
        // Fallback si l'API retourne directement le commentaire
        return Comment.fromJson(response.data);
      } else {
        // Fallback si la structure est différente
        return Comment.fromJson(response.data);
      }
    } on DioException catch (e) {
      print('❌ Erreur DioException: ${e.type}');
      print('📄 Status Code: ${e.response?.statusCode}');
      print('📄 Response Data: ${e.response?.data}');
      print('📄 Message: ${e.message}');

      // Gestion spécifique des erreurs HTTP
      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;

        switch (statusCode) {
          case 401:
            throw Exception('401: Non autorisé - Token invalide ou expiré');
          case 403:
            throw Exception('403: Accès interdit - Permissions insuffisantes');
          case 404:
            throw Exception('404: Lieu introuvable');
          case 422:
            if (responseData is Map) {
              final message = responseData['message'] as String?;

              // Cas spécial : utilisateur a déjà commenté
              if (message != null && message.contains('déjà commenté')) {
                throw Exception('ALREADY_COMMENTED: $message');
              }

              // Autres erreurs de validation
              if (responseData['errors'] != null) {
                final errors = responseData['errors'] as Map;
                final errorMessages =
                    errors.values.expand((e) => e as List).join(', ');
                throw Exception('422: Données invalides - $errorMessages');
              } else {
                throw Exception(
                    '422: Données invalides - ${message ?? 'Vérifiez le contenu et la note'}');
              }
            } else {
              throw Exception(
                  '422: Données invalides - Vérifiez le contenu et la note');
            }
          case 500:
            throw Exception('500: Erreur serveur - Réessayez plus tard');
          default:
            throw Exception(
                '$statusCode: ${responseData?['message'] ?? e.message}');
        }
      } else {
        // Erreur de connexion
        switch (e.type) {
          case DioExceptionType.connectionTimeout:
            throw Exception('Timeout de connexion - Vérifiez votre réseau');
          case DioExceptionType.receiveTimeout:
            throw Exception('Timeout de réception - Serveur trop lent');
          case DioExceptionType.connectionError:
            throw Exception('Erreur de connexion - Vérifiez votre réseau');
          default:
            throw Exception('Erreur réseau: ${e.message}');
        }
      }
    } catch (e) {
      print('❌ Erreur générale: $e');
      throw Exception('Failed to add comment: $e');
    }
  }

  Future<void> deleteComment(int commentId) async {
    try {
      await dio.delete('/comments/$commentId');
    } catch (e) {
      throw Exception('Failed to delete comment');
    }
  }

  Future<Comment> updateComment({
    required int commentId,
    required String content,
    required double rating,
  }) async {
    try {
      final response = await dio.put('/comments/$commentId', data: {
        'content': content,
        'rating': rating,
      });
      return Comment.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to update comment');
    }
  }
}
