import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:mbokatour/models/place.dart';

class PlaceRepository {
  final Dio dio;

  PlaceRepository(this.dio);

  Future<List<Place>> getDiscover() async {
    try {
      debugPrint('🌐 Appel API: /places/discover');
      final response = await dio.get('/places/discover');

      debugPrint('📡 Réponse API reçue - Status: ${response.statusCode}');
      debugPrint('📊 Type de données: ${response.data.runtimeType}');

      if (response.data is List) {
        final dataList = response.data as List;
        debugPrint('📋 Nombre d\'éléments: ${dataList.length}');

        if (dataList.isNotEmpty) {
          debugPrint('🔍 Premier élément: ${dataList.first}');
        }

        final places = dataList.map((json) {
          try {
            debugPrint('🔄 Parsing place: ${json['name']} (ID: ${json['id']})');
            return Place.fromJson(json);
          } catch (e) {
            debugPrint('❌ Erreur parsing place: $e');
            debugPrint('❌ JSON problématique: $json');
            rethrow;
          }
        }).toList();

        debugPrint('✅ ${places.length} places parsées avec succès');
        return places;
      } else {
        debugPrint('❌ Format de réponse inattendu: ${response.data}');
        throw Exception('Format de réponse API inattendu');
      }
    } catch (e) {
      debugPrint('❌ Erreur dans getDiscover: $e');
      throw Exception('Failed to load discover places: $e');
    }
  }

  Future<Place> findPlaceById(String id) async {
    try {
      final response = await dio.get('/places/$id');
      return Place.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load place');
    }
  }

  Future<List<Place>> filterByCategory(int categoryId) async {
    try {
      final response = await dio
          .get('/places', queryParameters: {'category_id': categoryId});
      return (response.data as List)
          .map((json) => Place.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load places by category');
    }
  }

  // Toggle like status for a place
  Future<Map<String, dynamic>> toggleLike(String placeId) async {
    try {
      final response = await dio.post('/places/likes/toggle', data: {
        'place_id': placeId,
      });
      return response.data;
    } catch (e) {
      throw Exception('Failed to toggle like');
    }
  }

  // Get like status for a place
  Future<Map<String, dynamic>> getLikeStatus(String placeId) async {
    try {
      final response = await dio.get('/places/$placeId/likes/status');
      return response.data;
    } catch (e) {
      throw Exception('Failed to get like status');
    }
  }

  // Add place to favorites
  Future<void> addToFavorite(String placeId) async {
    try {
      await dio.post('/user/favorites/places', data: {
        'place_id': placeId,
      });
    } catch (e) {
      throw Exception('Failed to add to favorites');
    }
  }

  // Remove place from favorites
  Future<void> removeFromFavorite(String placeId) async {
    try {
      await dio.delete('/user/favorites/places/$placeId');
    } catch (e) {
      throw Exception('Failed to remove from favorites');
    }
  }

  // Legacy method for backward compatibility
  Future<void> likePlace(String placeId) async {
    try {
      await toggleLike(placeId);
    } catch (e) {
      throw Exception('Failed to like place');
    }
  }

  // Get user's favorite places
  Future<List<Place>> getFavoritePlaces() async {
    try {
      final response = await dio.get('/user/favorites/places');
      return (response.data as List)
          .map((json) => Place.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load favorite places');
    }
  }

  // Get user's liked places
  Future<List<Place>> getLikedPlaces() async {
    try {
      final response = await dio.get('/user/likes/places');
      return (response.data as List)
          .map((json) => Place.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load liked places');
    }
  }

  Future<List<Place>> getNearbyPlaces(double latitude, double longitude,
      {double radiusInKm = 20.0}) async {
    try {
      final response = await dio.get('/places/nearby', queryParameters: {
        'latitude': latitude,
        'longitude': longitude,
        'radius': radiusInKm,
      });
      return (response.data as List)
          .map((json) => Place.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load nearby places');
    }
  }
}
