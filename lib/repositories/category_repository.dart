import 'package:dio/dio.dart';
import 'package:mbokatour/models/category.dart';

class CategoryRepository {
  final Dio dio;

  CategoryRepository(this.dio);

  Future<List<Category>> getCategories() async {
    try {
      final response = await dio.get('/categories');
      return (response.data as List)
          .map((json) => Category.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load categories');
    }
  }

  Future<Category> findCategoryById(int id) async {
    try {
      final response = await dio.get('/categories/$id');
      return Category.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load category');
    }
  }
}
