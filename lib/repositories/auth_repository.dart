import 'package:dio/dio.dart';
import 'package:mbokatour/models/user.dart';

class AuthRepository {
  final Dio dio;

  AuthRepository(this.dio);

  Future<AuthResponse> register({
    required String name,
    required String phoneNumber,
    required String password,
    required String passwordConfirmation,
    String? email,
  }) async {
    try {
      final response = await dio.post('/auth/register', data: {
        'name': name,
        'phone_number': phoneNumber,
        'password': password,
        'password_confirmation': passwordConfirmation,
        if (email != null && email.isNotEmpty) 'email': email,
      });
      return AuthResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to register user');
    }
  }

  Future<AuthResponse> login({
    required String login,
    required String password,
  }) async {
    try {
      final response = await dio.post('/auth/login', data: {
        'login': login,
        'password': password,
      });

      print('🔍 Raw API Response: ${response.data}');
      print('🔍 Status Code: ${response.statusCode}');

      final authResponse = AuthResponse.fromJson(response.data);
      print('🔍 Parsed AuthResponse: ${authResponse.accessToken}');

      return authResponse;
    } catch (e) {
      print('❌ Repository Error: $e');
      throw Exception('Failed to login user: $e');
    }
  }

  Future<User> getUser(String token) async {
    try {
      final response = await dio.get('/auth/user',
          options: Options(
            headers: {'Authorization': 'Bearer $token'},
          ));
      return User.fromJson(response.data['user']);
    } catch (e) {
      throw Exception('Failed to get user profile');
    }
  }

  Future<void> logout(String token) async {
    try {
      await dio.post('/auth/logout',
          options: Options(
            headers: {'Authorization': 'Bearer $token'},
          ));
    } catch (e) {
      throw Exception('Failed to logout user');
    }
  }

  Future<void> logoutAll(String token) async {
    try {
      await dio.post('/auth/logout-all',
          options: Options(
            headers: {'Authorization': 'Bearer $token'},
          ));
    } catch (e) {
      throw Exception('Failed to logout from all devices');
    }
  }
}
