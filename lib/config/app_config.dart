import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

class AppConfig {
  // Configuration de l'environnement
  static const bool isProd = true; // true en production, false en développement

  // URLs des APIs
  static const String _prodBaseUrl = 'https://mbokatour.com/api';

  // URLs de développement selon la plateforme
  static const String _devBaseUrlAndroid =
      'http://********:8000/api'; // Émulateur Android
  static const String _devBaseUrlIOS =
      'http://localhost:8000/api'; // Simulateur iOS
  static const String _devBaseUrlWeb = 'http://localhost:8000/api'; // Web

  // URL de base dynamique selon l'environnement et la plateforme
  static String get baseUrl {
    if (isProd) return _prodBaseUrl;

    // En développement, adapter selon la plateforme
    if (Platform.isAndroid) {
      return _devBaseUrlAndroid; // ******** pour émulateur Android
    } else if (Platform.isIOS) {
      return _devBaseUrlIOS; // localhost pour simulateur iOS
    } else {
      return _devBaseUrlWeb; // localhost pour web et autres plateformes
    }
  }

  // Informations sur l'environnement actuel
  static String get environment => isProd ? 'Production' : 'Développement';
  static String get apiHost {
    if (isProd) return 'mbokatour.com';

    // En développement, adapter selon la plateforme
    if (Platform.isAndroid) {
      return '********:8000'; // Émulateur Android
    } else {
      return 'localhost:8000'; // iOS, Web et autres
    }
  }

  // Configuration pour forcer l'environnement de développement (utile pour les tests)
  static bool _forceDev = false;
  static bool get isDevMode => _forceDev || !isProd;

  // Méthodes pour gérer l'environnement
  static void forceDevelopmentMode() {
    _forceDev = true;
  }

  static void resetEnvironmentMode() {
    _forceDev = false;
  }

  // URL de base avec prise en compte du mode forcé
  static String get effectiveBaseUrl {
    if (!isDevMode) return _prodBaseUrl;

    // En mode développement, adapter selon la plateforme
    if (Platform.isAndroid) {
      return _devBaseUrlAndroid;
    } else if (Platform.isIOS) {
      return _devBaseUrlIOS;
    } else {
      return _devBaseUrlWeb;
    }
  }

  // Méthode pour afficher les informations de debug
  static void printEnvironmentInfo() {
    debugPrint('🌍 Environnement: $environment');
    debugPrint('🔗 API Host: $apiHost');
    debugPrint('📡 Base URL: $baseUrl');
    debugPrint('🛠️ Mode Debug: ${!isProd}');
    if (_forceDev) debugPrint('⚠️ Mode développement forcé');
  }

  static const Map<int, Color> primary = {
    50: Color(0xFFFFFEEA),
    100: Color(0xFFFFFDC5),
    200: Color(0xFFFFFC87),
    300: Color(0xFFFFF448),
    400: Color(0xFFFFE71E),
    500: Color(0xFFFCC804),
    600: Color(0xFFE5A000),
    700: Color(0xFFB97004),
    800: Color(0xFF96560A),
    900: Color(0xFF7B460C),
    950: Color(0xFF472401), // facultatif : pas utilisé dans MaterialColor
  };
}
