# Implémentation des Vues et Likes - MbokaTour

## Vue d'ensemble
Documentation de l'implémentation de l'affichage des likes à côté des vues et de l'incrémentation automatique des vues.

## Fonctionnalités implémentées

### 1. Affichage des likes à côté des vues
- **HomeScreen**: Les cartes affichent maintenant les vues ET les likes
- **PlaceDetailScreen**: Les détails du lieu affichent les vues et likes dans le header

### 2. Incrémentation automatique des vues
- **HomeScreen**: Les vues sont incrémentées automatiquement quand une carte apparaît
- **PlaceDetailScreen**: Les vues sont incrémentées quand on ouvre les détails d'un lieu

## Modifications techniques

### PlaceController
```dart
// Méthode pour charger les détails (les vues sont automatiquement incrémentées par l'API)
Future<Place> loadPlaceDetails(String placeId) async {
  try {
    // L'API incrémente automatiquement les vues à chaque appel de findById
    final place = await repo.findPlaceById(placeId);
    debugPrint('📈 Place chargé avec vues incrémentées: ${place.name}');
    return place;
  } catch (e) {
    throw Exception('Failed to load place details: $e');
  }
}
```

### HomeScreen - Affichage des likes et vues
```dart
Row(
  children: [
    // Affichage des vues
    HeroIcon(HeroIcons.eye, style: HeroIconStyle.outline, color: Colors.white70, size: 14),
    const SizedBox(width: 6),
    Text('${place.viewsCount ?? 0}', style: TextStyle(...)),
    const SizedBox(width: 16),
    // Affichage des likes
    HeroIcon(HeroIcons.heart, style: HeroIconStyle.outline, color: Colors.white70, size: 14),
    const SizedBox(width: 6),
    Text('${place.likesCount ?? 0}', style: TextStyle(...)),
  ],
)
```

### HomeScreen - Incrémentation automatique des vues
```dart
// Variable pour tracker les lieux déjà vus
final Set<String> _viewedPlaces = {};

// Dans le cardBuilder du CardSwiper
cardBuilder: (context, index, horizontalOffsetPercentage, verticalOffsetPercentage) {
  if (index >= filteredPlaces.length) return null;

  final place = filteredPlaces[index];

  // Incrémenter les vues automatiquement quand la carte apparaît
  if (!_viewedPlaces.contains(place.id)) {
    _viewedPlaces.add(place.id);
    // Appeler findPlaceById pour incrémenter les vues (l'API le fait automatiquement)
    _placeController.findPlaceById(place.id).then((_) {
      debugPrint('📈 Vues incrémentées pour: ${place.name}');
    }).catchError((e) {
      debugPrint('Erreur lors de l\'incrémentation des vues: $e');
    });
  }

  return _buildPlaceCard(place);
}
```

### PlaceDetailScreen - Incrémentation des vues
```dart
Future<void> _loadPlaceData() async {
  // Incrémenter les vues dès l'ouverture des détails (l'API le fait automatiquement)
  _placeController.loadPlaceDetails(widget.place.id).then((_) {
    debugPrint('📈 Vues incrémentées pour: ${widget.place.name}');
  }).catchError((e) {
    debugPrint('Erreur lors de l\'incrémentation des vues: $e');
  });

  // Charger les autres données...
}
```

### PlaceDetailScreen - Affichage des vues et likes
```dart
// Dans le header, après l'adresse
Container(
  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  decoration: BoxDecoration(
    color: Colors.black.withValues(alpha: 0.4),
    borderRadius: BorderRadius.circular(20),
  ),
  child: Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      // Vues
      const Icon(Icons.visibility, color: Colors.white70, size: 14),
      const SizedBox(width: 4),
      Text('${place.viewsCount ?? 0}', style: TextStyle(...)),
      const SizedBox(width: 16),
      // Likes
      const Icon(Icons.favorite, color: Colors.white70, size: 14),
      const SizedBox(width: 4),
      Text('${place.likesCount ?? 0}', style: TextStyle(...)),
    ],
  ),
)
```

## API Comportement

### Incrémentation automatique des vues
L'API incrémente automatiquement les vues à chaque appel de :
```http
GET /api/places/{id}
```

**Réponse attendue:**
```json
{
  "id": "1",
  "name": "Lieu exemple",
  "views_count": 43,
  "likes_count": 15,
  // ... autres propriétés
}
```

## Comportement

### HomeScreen
1. **Affichage**: Chaque carte affiche maintenant "👁️ 123 ❤️ 45" au lieu de "👁️ 123 vues"
2. **Incrémentation**: Dès qu'une carte apparaît dans le CardSwiper, les vues sont incrémentées automatiquement
3. **Optimisation**: Chaque lieu n'est compté qu'une seule fois par session grâce au Set `_viewedPlaces`

### PlaceDetailScreen
1. **Affichage**: Le header affiche les vues et likes sous l'adresse
2. **Incrémentation**: Dès l'ouverture de l'écran, les vues sont incrémentées
3. **Design**: Intégration harmonieuse avec le design existant

## Gestion des erreurs
- Les erreurs d'incrémentation des vues sont loggées mais n'interrompent pas l'expérience utilisateur
- Les appels API sont asynchrones pour ne pas bloquer l'interface
- Valeurs par défaut (0) si les données ne sont pas disponibles

## Tests recommandés
1. Vérifier que les vues s'incrémentent quand on swipe les cartes
2. Vérifier que les vues s'incrémentent quand on ouvre les détails
3. Vérifier que l'affichage des likes et vues est correct
4. Tester le comportement en cas d'erreur réseau
5. Vérifier qu'un lieu n'est compté qu'une fois par session dans HomeScreen

## Performance
- Appels API asynchrones pour ne pas bloquer l'UI
- Tracking local pour éviter les appels API redondants
- Gestion gracieuse des erreurs réseau
