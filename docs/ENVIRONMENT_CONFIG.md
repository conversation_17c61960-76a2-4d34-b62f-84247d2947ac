# Configuration d'environnement - MbokaTour

## Vue d'ensemble
Guide de configuration des environnements de développement et production pour MbokaTour.

## Environnements

### 1. Développement (Development)
- **API URL**: `http://localhost:8000/api`
- **Émulateur Android**: `http://********:8000/api`
- **Debug**: Activé
- **Logs**: Verbeux

### 2. Production
- **API URL**: `https://mbokatour.com/api`
- **Debug**: Désactivé
- **Logs**: Erreurs uniquement
- **Optimisations**: Activées

## Configuration dans Flutter

### app_config.dart
```dart
class AppConfig {
  // Détection automatique de l'environnement
  static const bool isProd = bool.fromEnvironment('dart.vm.product');
  
  // URLs d'API
  static String get baseUrl {
    if (isProd) {
      return 'https://mbokatour.com/api';
    } else {
      // Développement local
      return 'http://********:8000/api'; // Émulateur Android
      // return 'http://localhost:8000/api'; // Simulateur iOS
    }
  }
  
  // Configuration des timeouts
  static Duration get connectTimeout {
    return isProd 
      ? const Duration(seconds: 10)
      : const Duration(seconds: 30);
  }
  
  // Niveau de logs
  static bool get enableLogs => !isProd;
  
  // Configuration de debug
  static bool get showDebugBanner => !isProd;
  
  // Couleurs de l'application
  static const Map<int, Color> primary = {
    50: Color(0xFFE8F5E8),
    100: Color(0xFFC8E6C9),
    200: Color(0xFFA5D6A7),
    300: Color(0xFF81C784),
    400: Color(0xFF66BB6A),
    500: Color(0xFF4CAF50),
    600: Color(0xFF43A047),
    700: Color(0xFF388E3C),
    800: Color(0xFF2E7D32),
    900: Color(0xFF1B5E20),
  };
}
```

## Variables d'environnement

### 1. Fichier .env (optionnel)
```env
# Développement
API_BASE_URL=http://localhost:8000/api
GOOGLE_MAPS_API_KEY=your_dev_api_key
DEBUG_MODE=true

# Production
# API_BASE_URL=https://mbokatour.com/api
# GOOGLE_MAPS_API_KEY=your_prod_api_key
# DEBUG_MODE=false
```

### 2. Configuration Dart
```dart
class EnvironmentConfig {
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://********:8000/api',
  );
  
  static const String googleMapsApiKey = String.fromEnvironment(
    'GOOGLE_MAPS_API_KEY',
    defaultValue: '',
  );
  
  static const bool debugMode = bool.fromEnvironment(
    'DEBUG_MODE',
    defaultValue: true,
  );
}
```

## Configuration par plateforme

### Android

#### build.gradle (app level)
```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.mbokatour.app"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    
    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            applicationIdSuffix ".debug"
        }
        
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
}
```

#### AndroidManifest.xml (Debug)
```xml
<!-- android/app/src/debug/AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-permission android:name="android.permission.INTERNET"/>
    <application android:usesCleartextTraffic="true" />
</manifest>
```

#### AndroidManifest.xml (Release)
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    
    <application
        android:label="MbokaTour"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:usesCleartextTraffic="false">
        
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_api_key" />
    </application>
</manifest>
```

### iOS

#### Info.plist
```xml
<dict>
    <key>CFBundleName</key>
    <string>MbokaTour</string>
    
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>Cette app utilise la localisation pour vous montrer les lieux à proximité.</string>
    
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
        <key>NSExceptionDomains</key>
        <dict>
            <key>localhost</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <true/>
            </dict>
        </dict>
    </dict>
</dict>
```

## Scripts de build

### 1. Build de développement
```bash
#!/bin/bash
# scripts/build_dev.sh

echo "🔨 Building MbokaTour for Development..."

# Nettoyer le cache
flutter clean
flutter pub get

# Build debug
flutter build apk --debug

echo "✅ Development build completed!"
```

### 2. Build de production
```bash
#!/bin/bash
# scripts/build_prod.sh

echo "🚀 Building MbokaTour for Production..."

# Nettoyer le cache
flutter clean
flutter pub get

# Analyser le code
flutter analyze

# Build release
flutter build apk --release
flutter build appbundle --release

echo "✅ Production build completed!"
```

### 3. Script de déploiement
```bash
#!/bin/bash
# scripts/deploy.sh

ENV=${1:-staging}

echo "🚀 Deploying to $ENV environment..."

case $ENV in
  "staging")
    flutter build apk --release --dart-define=ENV=staging
    ;;
  "production")
    flutter build appbundle --release --dart-define=ENV=production
    ;;
  *)
    echo "❌ Unknown environment: $ENV"
    exit 1
    ;;
esac

echo "✅ Deployment to $ENV completed!"
```

## Configuration des services

### 1. Service d'API
```dart
class ApiService {
  static late Dio _dio;
  
  static void initialize() {
    _dio = Dio();
    
    // Configuration de base
    _dio.options.baseUrl = AppConfig.baseUrl;
    _dio.options.connectTimeout = AppConfig.connectTimeout;
    _dio.options.receiveTimeout = AppConfig.connectTimeout;
    
    // Intercepteurs selon l'environnement
    if (AppConfig.enableLogs) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
      ));
    }
    
    // Intercepteur d'authentification
    _dio.interceptors.add(AuthInterceptor());
  }
}
```

### 2. Service de logs
```dart
class LogService {
  static void log(String message, {LogLevel level = LogLevel.info}) {
    if (!AppConfig.enableLogs) return;
    
    final timestamp = DateTime.now().toIso8601String();
    final prefix = _getLevelPrefix(level);
    
    print('[$timestamp] $prefix $message');
  }
  
  static String _getLevelPrefix(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🐛';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
    }
  }
}

enum LogLevel { debug, info, warning, error }
```

## Tests par environnement

### 1. Tests de développement
```bash
# Tests unitaires
flutter test

# Tests d'intégration sur émulateur
flutter test integration_test/ -d emulator-5554
```

### 2. Tests de production
```bash
# Build de test
flutter build apk --release --dart-define=ENV=staging

# Tests sur appareil physique
flutter test integration_test/ -d physical_device
```

## Monitoring

### 1. Métriques de performance
```dart
class PerformanceMonitor {
  static void trackApiCall(String endpoint, Duration duration) {
    if (AppConfig.isProd) {
      // Envoyer les métriques à un service de monitoring
      _sendMetrics(endpoint, duration);
    } else {
      LogService.log('API Call: $endpoint took ${duration.inMilliseconds}ms');
    }
  }
}
```

### 2. Crash reporting
```dart
void main() async {
  if (AppConfig.isProd) {
    // Initialiser le crash reporting pour la production
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  }
  
  runApp(MyApp());
}
```

## Checklist de déploiement

### Avant le déploiement
- [ ] Tests unitaires passent
- [ ] Tests d'intégration passent
- [ ] Analyse statique du code (flutter analyze)
- [ ] Configuration de production vérifiée
- [ ] Clés API de production configurées
- [ ] Certificats de signature configurés

### Après le déploiement
- [ ] Tests de fumée sur l'environnement cible
- [ ] Vérification des logs d'erreur
- [ ] Monitoring des performances
- [ ] Tests utilisateur

## Ressources utiles
- [Flutter Build Modes](https://docs.flutter.dev/testing/build-modes)
- [Environment Variables in Flutter](https://docs.flutter.dev/deployment/flavors)
- [Android Build Configuration](https://docs.flutter.dev/deployment/android)
