# Implémentation Likes et Favoris - MbokaTour

## Vue d'ensemble
Documentation de l'implémentation des fonctionnalités de likes et favoris dans MbokaTour.

## Architecture

### Endpoints API

#### Likes
```http
POST /api/places/likes/toggle
Authorization: Bearer {token}
Content-Type: application/json

{
  "place_id": 1
}
```

**Réponse:**
```json
{
  "success": true,
  "liked": true,
  "likes_count": 42
}
```

#### Favoris
```http
# Ajouter aux favoris
POST /api/user/favorites/places
Authorization: Bearer {token}
Content-Type: application/json

{
  "place_id": 1
}

# Supprimer des favoris
DELETE /api/user/favorites/places/{place_id}
Authorization: Bearer {token}

# Liste des favoris
GET /api/user/favorites/places
Authorization: Bearer {token}
```

## Implémentation Flutter

### 1. Repository Layer

#### PlaceRepository
```dart
class PlaceRepository {
  final ApiService _apiService;
  
  PlaceRepository(this._apiService);
  
  Future<Map<String, dynamic>> toggleLike(String placeId) async {
    try {
      final response = await _apiService.post(
        '/places/likes/toggle',
        data: {'place_id': placeId},
      );
      return response.data;
    } catch (e) {
      throw Exception('Failed to toggle like: $e');
    }
  }
  
  Future<void> addToFavorites(String placeId) async {
    try {
      await _apiService.post(
        '/user/favorites/places',
        data: {'place_id': placeId},
      );
    } catch (e) {
      throw Exception('Failed to add to favorites: $e');
    }
  }
  
  Future<void> removeFromFavorites(String placeId) async {
    try {
      await _apiService.delete('/user/favorites/places/$placeId');
    } catch (e) {
      throw Exception('Failed to remove from favorites: $e');
    }
  }
  
  Future<List<Place>> getFavorites() async {
    try {
      final response = await _apiService.get('/user/favorites/places');
      return (response.data['data'] as List)
          .map((json) => Place.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to get favorites: $e');
    }
  }
}
```

### 2. Controller Layer

#### PlaceController
```dart
class PlaceController {
  final PlaceRepository _repository;
  
  PlaceController(this._repository);
  
  Future<bool> toggleLike(Place place) async {
    try {
      final result = await _repository.toggleLike(place.id);
      
      // Mettre à jour l'état local
      place.isLiked = result['liked'];
      place.likesCount = result['likes_count'];
      
      return result['liked'];
    } catch (e) {
      throw Exception('Failed to toggle like: $e');
    }
  }
  
  Future<bool> toggleFavorite(Place place) async {
    try {
      if (place.isFavorite) {
        await _repository.removeFromFavorites(place.id);
        place.isFavorite = false;
      } else {
        await _repository.addToFavorites(place.id);
        place.isFavorite = true;
      }
      
      return place.isFavorite;
    } catch (e) {
      throw Exception('Failed to toggle favorite: $e');
    }
  }
}
```

### 3. UI Implementation

#### PlaceCard avec likes et favoris
```dart
class PlaceCard extends StatefulWidget {
  final Place place;
  final VoidCallback? onTap;
  
  const PlaceCard({
    super.key,
    required this.place,
    this.onTap,
  });
  
  @override
  State<PlaceCard> createState() => _PlaceCardState();
}

class _PlaceCardState extends State<PlaceCard> {
  late PlaceController _placeController;
  bool _isLiking = false;
  bool _isFavoriting = false;
  
  @override
  void initState() {
    super.initState();
    _placeController = PlaceController(
      PlaceRepository(ApiService.instance),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          // Image du lieu
          _buildPlaceImage(),
          
          // Informations du lieu
          _buildPlaceInfo(),
          
          // Actions (like et favoris)
          _buildActionButtons(),
        ],
      ),
    );
  }
  
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Bouton Like
        _buildLikeButton(),
        
        // Bouton Favoris
        _buildFavoriteButton(),
      ],
    );
  }
  
  Widget _buildLikeButton() {
    return AuthRequiredActionButton(
      onPressed: _isLiking ? null : _handleLike,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.place.isLiked ? Icons.favorite : Icons.favorite_border,
            color: widget.place.isLiked ? Colors.red : Colors.grey,
          ),
          const SizedBox(width: 4),
          Text('${widget.place.likesCount}'),
          if (_isLiking) ...[
            const SizedBox(width: 8),
            const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildFavoriteButton() {
    return AuthRequiredActionButton(
      onPressed: _isFavoriting ? null : _handleFavorite,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.place.isFavorite ? Icons.bookmark : Icons.bookmark_border,
            color: widget.place.isFavorite ? AppConfig.primary[600] : Colors.grey,
          ),
          if (_isFavoriting) ...[
            const SizedBox(width: 8),
            const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ],
        ],
      ),
    );
  }
  
  Future<void> _handleLike() async {
    if (!AuthService.instance.isAuthenticated) {
      context.showAuthRequiredSnackbar(
        message: 'Connectez-vous pour liker ce lieu',
      );
      return;
    }
    
    setState(() => _isLiking = true);
    
    try {
      await _placeController.toggleLike(widget.place);
      setState(() {}); // Rafraîchir l'UI
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur: $e')),
      );
    } finally {
      setState(() => _isLiking = false);
    }
  }
  
  Future<void> _handleFavorite() async {
    if (!AuthService.instance.isAuthenticated) {
      context.showAuthRequiredSnackbar(
        message: 'Connectez-vous pour ajouter aux favoris',
      );
      return;
    }
    
    setState(() => _isFavoriting = true);
    
    try {
      await _placeController.toggleFavorite(widget.place);
      setState(() {}); // Rafraîchir l'UI
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.place.isFavorite 
              ? 'Ajouté aux favoris' 
              : 'Retiré des favoris'
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur: $e')),
      );
    } finally {
      setState(() => _isFavoriting = false);
    }
  }
}
```

### 4. Écran des favoris

#### FavoritesScreen
```dart
class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});
  
  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  late PlaceController _placeController;
  List<Place> _favoritePlaces = [];
  bool _isLoading = true;
  String? _errorMessage;
  
  @override
  void initState() {
    super.initState();
    _placeController = PlaceController(
      PlaceRepository(ApiService.instance),
    );
    _loadFavorites();
  }
  
  Future<void> _loadFavorites() async {
    if (!AuthService.instance.isAuthenticated) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Vous devez être connecté pour voir vos favoris';
      });
      return;
    }
    
    try {
      final favorites = await PlaceRepository(ApiService.instance).getFavorites();
      setState(() {
        _favoritePlaces = favorites;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du chargement des favoris: $e';
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mes Favoris'),
        actions: [
          if (_favoritePlaces.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: _showClearAllDialog,
            ),
        ],
      ),
      body: _buildBody(),
    );
  }
  
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(_errorMessage!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFavorites,
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }
    
    if (_favoritePlaces.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_border, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucun favori pour le moment',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Explorez des lieux et ajoutez-les à vos favoris',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }
    
    return RefreshIndicator(
      onRefresh: _loadFavorites,
      child: ListView.builder(
        itemCount: _favoritePlaces.length,
        itemBuilder: (context, index) {
          final place = _favoritePlaces[index];
          return PlaceCard(
            place: place,
            onTap: () => _navigateToPlaceDetail(place),
          );
        },
      ),
    );
  }
  
  void _navigateToPlaceDetail(Place place) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailScreen(place: place),
      ),
    );
  }
  
  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Supprimer tous les favoris'),
          content: const Text(
            'Êtes-vous sûr de vouloir supprimer tous vos lieux favoris ? Cette action ne peut pas être annulée.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _clearAllFavorites();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Supprimer'),
            ),
          ],
        );
      },
    );
  }
  
  Future<void> _clearAllFavorites() async {
    // Implémentation pour supprimer tous les favoris
    for (final place in _favoritePlaces) {
      try {
        await _placeController.toggleFavorite(place);
      } catch (e) {
        // Log l'erreur mais continue
        print('Erreur lors de la suppression du favori ${place.id}: $e');
      }
    }
    
    setState(() {
      _favoritePlaces.clear();
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tous les favoris ont été supprimés'),
      ),
    );
  }
}
```

## Optimisations

### 1. Mise à jour optimiste
```dart
Future<void> _handleLikeOptimistic() async {
  // Mise à jour immédiate de l'UI
  final originalState = widget.place.isLiked;
  final originalCount = widget.place.likesCount;
  
  setState(() {
    widget.place.isLiked = !widget.place.isLiked;
    widget.place.likesCount += widget.place.isLiked ? 1 : -1;
  });
  
  try {
    await _placeController.toggleLike(widget.place);
  } catch (e) {
    // Restaurer l'état original en cas d'erreur
    setState(() {
      widget.place.isLiked = originalState;
      widget.place.likesCount = originalCount;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Erreur: $e')),
    );
  }
}
```

### 2. Cache local
```dart
class FavoritesCache {
  static final Set<String> _favoriteIds = {};
  
  static bool isFavorite(String placeId) {
    return _favoriteIds.contains(placeId);
  }
  
  static void addFavorite(String placeId) {
    _favoriteIds.add(placeId);
  }
  
  static void removeFavorite(String placeId) {
    _favoriteIds.remove(placeId);
  }
  
  static void clear() {
    _favoriteIds.clear();
  }
}
```

## Gestion des erreurs

### 1. Erreurs réseau
- Retry automatique pour les échecs temporaires
- Messages d'erreur utilisateur-friendly
- Fallback vers le cache local

### 2. Erreurs d'authentification
- Redirection automatique vers la connexion
- Sauvegarde de l'action pour après la connexion
- Messages informatifs

## Tests

### 1. Tests unitaires
```dart
group('PlaceController Tests', () {
  test('should toggle like successfully', () async {
    // Arrange
    final mockRepository = MockPlaceRepository();
    final controller = PlaceController(mockRepository);
    final place = Place(id: '1', isLiked: false, likesCount: 0);
    
    when(mockRepository.toggleLike('1'))
        .thenAnswer((_) async => {'liked': true, 'likes_count': 1});
    
    // Act
    final result = await controller.toggleLike(place);
    
    // Assert
    expect(result, true);
    expect(place.isLiked, true);
    expect(place.likesCount, 1);
  });
});
```

### 2. Tests d'intégration
```dart
testWidgets('should show auth dialog when not authenticated', (tester) async {
  // Arrange
  AuthService.instance.logout();
  final place = Place(id: '1', isLiked: false);
  
  // Act
  await tester.pumpWidget(MaterialApp(
    home: PlaceCard(place: place),
  ));
  
  await tester.tap(find.byIcon(Icons.favorite_border));
  await tester.pumpAndSettle();
  
  // Assert
  expect(find.text('Connectez-vous pour liker ce lieu'), findsOneWidget);
});
```
