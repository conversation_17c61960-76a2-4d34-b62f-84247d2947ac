# Configuration Émulateur Android - MbokaTour

## Vue d'ensemble
Guide pour configurer l'émulateur Android pour le développement de MbokaTour.

## Prérequis
- Android Studio installé
- SDK Android configuré
- Flutter SDK installé

## Configuration de l'émulateur

### 1. Création d'un AVD (Android Virtual Device)

#### Via Android Studio
1. Ouvrir Android Studio
2. Aller dans **Tools > AVD Manager**
3. Cliquer sur **Create Virtual Device**
4. Sélectionner un appareil (recommandé : Pixel 6)
5. Choisir une image système (API 30+ recommandé)
6. Configurer les paramètres avancés

#### Paramètres recommandés
```
Device: Pixel 6
API Level: 33 (Android 13)
RAM: 4096 MB
Internal Storage: 8 GB
SD Card: 1 GB
```

### 2. Configuration réseau pour l'API

#### Adresses IP importantes
```bash
# Pour l'émulateur Android
API_URL=http://********:8000/api

# Pour appareil physique (remplacer par votre IP locale)
API_URL=http://192.168.1.XXX:8000/api
```

#### Configuration dans app_config.dart
```dart
class AppConfig {
  static const bool isProd = bool.fromEnvironment('dart.vm.product');
  
  static String get baseUrl {
    if (isProd) {
      return 'https://mbokatour.com/api';
    } else {
      // Pour émulateur Android
      return 'http://********:8000/api';
    }
  }
}
```

### 3. Permissions requises

#### AndroidManifest.xml
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

### 4. Configuration de la géolocalisation

#### Localisation simulée
Dans l'émulateur :
1. Aller dans **Settings > Location**
2. Activer la localisation
3. Dans Android Studio : **Tools > Android > AVD Manager**
4. Cliquer sur l'icône "..." de votre AVD
5. Sélectionner **Extended Controls**
6. Aller dans **Location** et définir les coordonnées

#### Coordonnées de Kinshasa
```
Latitude: -4.4419
Longitude: 15.2663
```

## Commandes utiles

### 1. Lancement de l'émulateur
```bash
# Lister les AVDs disponibles
flutter emulators

# Lancer un émulateur spécifique
flutter emulators --launch <emulator_id>

# Lancer l'application sur l'émulateur
flutter run
```

### 2. Debugging
```bash
# Logs de l'application
flutter logs

# Logs système Android
adb logcat

# Informations sur l'appareil
flutter devices

# Hot reload
r (dans le terminal Flutter)

# Hot restart
R (dans le terminal Flutter)
```

### 3. Installation et désinstallation
```bash
# Installer l'APK
flutter install

# Désinstaller l'application
adb uninstall com.example.mboka_tour_app
```

## Optimisation des performances

### 1. Configuration de l'émulateur
```
Graphics: Hardware - GLES 2.0
Multi-Core CPU: 4 cores
RAM: 4096 MB (minimum 2048 MB)
VM Heap: 256 MB
```

### 2. Accélération matérielle
- Activer **Hardware Acceleration** dans les paramètres AVD
- Utiliser **Intel HAXM** (Windows/Mac) ou **KVM** (Linux)

### 3. Optimisations Flutter
```bash
# Build en mode debug optimisé
flutter run --debug

# Build en mode profile pour les tests de performance
flutter run --profile

# Build en mode release pour les tests finaux
flutter run --release
```

## Résolution des problèmes courants

### 1. Problème de connexion API
```bash
# Vérifier la connectivité
adb shell ping ********

# Tester l'API directement
curl http://********:8000/api/health
```

### 2. Problème de géolocalisation
- Vérifier que la localisation est activée dans l'émulateur
- S'assurer que les permissions sont accordées
- Tester avec des coordonnées simulées

### 3. Problème de performance
```bash
# Nettoyer le cache Flutter
flutter clean

# Reconstruire l'application
flutter pub get
flutter run
```

### 4. Problème de hot reload
```bash
# Redémarrer l'application
R (dans le terminal Flutter)

# Redémarrer complètement
flutter run
```

## Configuration pour les tests

### 1. Tests automatisés
```bash
# Lancer les tests sur l'émulateur
flutter test integration_test/

# Tests avec coverage
flutter test --coverage
```

### 2. Tests de performance
```bash
# Mode profile pour les tests de performance
flutter run --profile

# Analyse des performances
flutter analyze
```

### 3. Tests de l'API
```bash
# Tester la connectivité API
dart run test_api_connection.dart
```

## Conseils de développement

### 1. Workflow recommandé
1. Démarrer l'émulateur
2. Lancer l'API Laravel locale
3. Exécuter `flutter run`
4. Utiliser hot reload pour les modifications

### 2. Debugging efficace
- Utiliser les **Flutter Inspector** dans Android Studio/VS Code
- Activer les **Debug Banners** en mode debug
- Utiliser `print()` pour les logs simples
- Utiliser `debugPrint()` pour les logs détaillés

### 3. Tests sur différentes tailles d'écran
Créer plusieurs AVDs avec différentes résolutions :
- Phone (5.5")
- Tablet (10")
- Foldable

### 4. Tests de connectivité
- Tester avec WiFi activé/désactivé
- Tester avec données mobiles simulées
- Tester les timeouts de connexion

## Ressources utiles
- [Documentation Flutter Android](https://docs.flutter.dev/deployment/android)
- [Android Emulator Guide](https://developer.android.com/studio/run/emulator)
- [Flutter Debugging](https://docs.flutter.dev/testing/debugging)
