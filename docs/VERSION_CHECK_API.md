# API de Vérification de Version - MbokaTour

## Vue d'ensemble
Documentation de l'endpoint API pour la vérification de version et les mises à jour forcées de l'application MbokaTour.

## Endpoint

### GET /api/app/version

Vérifie la version de l'application et retourne les informations de mise à jour.

#### Paramètres de requête

| Paramètre | Type | Obligatoire | Description |
|-----------|------|-------------|-------------|
| `current_version` | string | Oui | Version actuelle de l'app (ex: "1.0.0") |
| `platform` | string | Oui | Plateforme ("android", "ios") |

#### Exemple de requête

```http
GET /api/app/version?current_version=1.0.0&platform=android
```

#### Réponse

```json
{
  "minimum_version": "1.0.0",
  "latest_version": "1.1.0",
  "force_update": false,
  "has_update": true,
  "update_message": "Une nouvelle version de MbokaTour est disponible avec de nouvelles fonctionnalités !",
  "download_url": "https://play.google.com/store/apps/details?id=com.mbokatour.app",
  "features": [
    "Nouvelles destinations à découvrir",
    "Interface améliorée",
    "Corrections de bugs"
  ]
}
```

#### Structure de la réponse

| Champ | Type | Description |
|-------|------|-------------|
| `minimum_version` | string | Version minimale requise pour utiliser l'app |
| `latest_version` | string | Dernière version disponible |
| `force_update` | boolean | Si true, l'utilisateur DOIT mettre à jour |
| `has_update` | boolean | Si true, une mise à jour est disponible |
| `update_message` | string | Message à afficher à l'utilisateur |
| `download_url` | string | URL vers le store (Play Store/App Store) |
| `features` | array | Liste des nouvelles fonctionnalités |

## Logique de mise à jour

### Mise à jour forcée (`force_update: true`)
- L'utilisateur ne peut pas utiliser l'app
- Le dialogue ne peut pas être fermé
- Pas de bouton "Plus tard"
- Redirection obligatoire vers le store

### Mise à jour optionnelle (`has_update: true`, `force_update: false`)
- L'utilisateur peut choisir
- Bouton "Plus tard" disponible
- L'app continue de fonctionner

### Aucune mise à jour
- L'app continue normalement
- Aucun dialogue affiché

## Exemples d'implémentation serveur

### PHP (Laravel)

```php
<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class AppVersionController extends Controller
{
    public function checkVersion(Request $request)
    {
        $currentVersion = $request->query('current_version');
        $platform = $request->query('platform');
        
        // Configuration des versions (à adapter selon vos besoins)
        $config = [
            'android' => [
                'minimum_version' => '1.0.0',
                'latest_version' => '1.1.0',
                'download_url' => 'https://play.google.com/store/apps/details?id=com.mbokatour.app'
            ],
            'ios' => [
                'minimum_version' => '1.0.0',
                'latest_version' => '1.1.0',
                'download_url' => 'https://apps.apple.com/app/mbokatour/id123456789'
            ]
        ];
        
        $platformConfig = $config[$platform] ?? $config['android'];
        
        $forceUpdate = version_compare($currentVersion, $platformConfig['minimum_version'], '<');
        $hasUpdate = version_compare($currentVersion, $platformConfig['latest_version'], '<');
        
        return response()->json([
            'minimum_version' => $platformConfig['minimum_version'],
            'latest_version' => $platformConfig['latest_version'],
            'force_update' => $forceUpdate,
            'has_update' => $hasUpdate,
            'update_message' => $forceUpdate 
                ? 'Une mise à jour obligatoire est disponible pour continuer à utiliser MbokaTour.'
                : ($hasUpdate ? 'Une nouvelle version de MbokaTour est disponible !' : null),
            'download_url' => $platformConfig['download_url'],
            'features' => $hasUpdate ? [
                'Nouvelles destinations à découvrir',
                'Interface améliorée',
                'Corrections de bugs'
            ] : null
        ]);
    }
}
```


## Configuration recommandée

### Base de données (optionnel)
Vous pouvez stocker la configuration dans une base de données pour plus de flexibilité :

```sql
CREATE TABLE app_versions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform ENUM('android', 'ios') NOT NULL,
    minimum_version VARCHAR(20) NOT NULL,
    latest_version VARCHAR(20) NOT NULL,
    download_url VARCHAR(255) NOT NULL,
    update_message TEXT,
    features JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Variables d'environnement
```env
ANDROID_MINIMUM_VERSION=1.0.0
ANDROID_LATEST_VERSION=1.1.0
ANDROID_STORE_URL=https://play.google.com/store/apps/details?id=com.mbokatour.app

IOS_MINIMUM_VERSION=1.0.0
IOS_LATEST_VERSION=1.1.0
IOS_STORE_URL=https://apps.apple.com/app/mbokatour/id123456789
```

## Test de l'implémentation

### Tester avec curl

```bash
# Test mise à jour disponible
curl "https://mbokatour.com/api/app/version?current_version=1.0.0&platform=android"

# Test mise à jour forcée
curl "https://mbokatour.com/api/app/version?current_version=0.9.0&platform=android"

# Test aucune mise à jour
curl "https://mbokatour.com/api/app/version?current_version=1.1.0&platform=android"
```

## Notes importantes

1. **Sécurité** : Cet endpoint peut être public (pas d'authentification requise)
2. **Cache** : Considérez mettre en cache la réponse côté serveur
3. **Monitoring** : Surveillez les appels pour détecter les versions obsolètes
4. **Rollback** : Gardez la possibilité de revenir en arrière en cas de problème
5. **Graduel** : Implémentez un déploiement progressif si nécessaire
