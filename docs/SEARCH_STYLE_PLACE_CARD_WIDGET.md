# SearchStylePlaceCard Widget

## Vue d'ensemble

Le `SearchStylePlaceCard` est un widget réutilisable qui affiche les informations d'un lieu dans un format de carte horizontal, similaire au style utilisé dans les écrans de recherche. Il supporte les images, vidéos, actions utilisateur et est optimisé pour une utilisation dans différents contextes.

## Fonctionnalités

### ✅ **Médias supportés**
- **Images** : Affichage avec cache et placeholder
- **Vidéos** : Lecture avec overlay et contrôles
- **Fallback** : Gestion des erreurs avec images alternatives

### ✅ **Informations affichées**
- Nom du lieu (titre principal)
- Description (2 lignes maximum)
- Localisation (location → city → address)
- Prix avec badge coloré (vert=gratuit, bleu=payant)
- Note avec étoile (générée automatiquement)
- Distance (optionnelle)

### ✅ **Actions utilisateur**
- Navigation vers les détails (tap)
- Bouton favoris (optionnel)
- Bouton like (optionnel)
- Actions configurables

## API du Widget

```dart
SearchStylePlaceCard({
  required Place place,              // Lieu à afficher
  String? distance,                  // Distance optionnelle
  VoidCallback? onFavorite,          // Action favoris
  VoidCallback? onLike,              // Action like
  bool isFavorite = false,           // État favori
  bool isLiked = false,              // État like
  bool showActions = false,          // Afficher les actions
  EdgeInsets? margin,                // Marge personnalisée
  VideoPlayerController? videoController, // Contrôleur vidéo réutilisable
})
```

## Paramètres

| Paramètre | Type | Requis | Description |
|-----------|------|--------|-------------|
| `place` | `Place` | ✅ | Objet lieu avec toutes les informations |
| `distance` | `String?` | ❌ | Distance affichée (ex: "2.5 km") |
| `onFavorite` | `VoidCallback?` | ❌ | Callback pour action favoris |
| `onLike` | `VoidCallback?` | ❌ | Callback pour action like |
| `isFavorite` | `bool` | ❌ | État actuel des favoris (défaut: false) |
| `isLiked` | `bool` | ❌ | État actuel des likes (défaut: false) |
| `showActions` | `bool` | ❌ | Afficher boutons d'action (défaut: false) |
| `margin` | `EdgeInsets?` | ❌ | Marge personnalisée |
| `videoController` | `VideoPlayerController?` | ❌ | Contrôleur vidéo pour réutilisation |

## Cas d'usage

### 1. **Écran de recherche** (Actions désactivées)
```dart
SearchStylePlaceCard(
  place: place,
  showActions: false, // Pas d'actions, juste navigation
)
```

### 2. **Écran des favoris** (Actions activées)
```dart
SearchStylePlaceCard(
  place: place,
  isFavorite: true,
  isLiked: place.isLiked ?? false,
  showActions: true,
  onFavorite: () => _removeFavorite(place.id),
  onLike: () => _toggleLike(place.id),
)
```

### 3. **Lieux à proximité** (Avec distance)
```dart
SearchStylePlaceCard(
  place: place,
  distance: "2.5 km",
  showActions: true,
  isFavorite: place.isFavorited ?? false,
  isLiked: place.isLiked ?? false,
  onFavorite: () => _toggleFavorite(place.id),
  onLike: () => _toggleLike(place.id),
)
```

### 4. **Recommandations** (Simple)
```dart
SearchStylePlaceCard(
  place: place,
  showActions: false,
  margin: const EdgeInsets.only(bottom: 8),
)
```

## Design et Layout

### Structure visuelle
```
┌─────────────────────────────────────────┐
│ [IMG] Nom du lieu               [→]     │
│ 80x80 Description courte...             │
│       📍 Localisation          2.5km    │
│       💰 Prix    ⭐ Note    ❤️ 👍     │
└─────────────────────────────────────────┘
```

### Éléments du design
- **Image/Vidéo** : 80x80px avec coins arrondis
- **Titre** : Police 16px, gras, 1 ligne
- **Description** : Police 14px, gris, 2 lignes max
- **Localisation** : Icône + texte 12px, gris
- **Prix** : Badge coloré avec texte 11px
- **Note** : Étoile + valeur 12px
- **Actions** : Icônes 18px avec zones de tap

## Support des vidéos

### Fonctionnalités vidéo
- **Initialisation automatique** des contrôleurs vidéo
- **Réutilisation** des contrôleurs existants
- **Overlay de lecture** avec icône play
- **Fallback** vers images alternatives en cas d'erreur
- **Gestion mémoire** optimisée

### Gestion des contrôleurs
```dart
// Réutilisation d'un contrôleur existant
SearchStylePlaceCard(
  place: videoPlace,
  videoController: existingController,
)

// Création automatique d'un nouveau contrôleur
SearchStylePlaceCard(
  place: videoPlace,
  // Le widget crée et gère automatiquement le contrôleur
)
```

## Optimisations

### Performance
- **Cache d'images** avec CachedNetworkImage
- **Lazy loading** des vidéos
- **Réutilisation** des contrôleurs vidéo
- **Gestion mémoire** automatique

### UX
- **Feedback visuel** pour les interactions
- **États de chargement** avec placeholders
- **Gestion d'erreurs** gracieuse
- **Navigation fluide** vers les détails

## Intégration

### Import
```dart
import 'package:mbokatour/widgets/search_style_place_card.dart';
```

### Dépendances requises
- `cached_network_image` : Cache d'images
- `video_player` : Lecture vidéo
- `mbokatour/models/place.dart` : Modèle de données
- `mbokatour/config/app_config.dart` : Configuration couleurs

## Exemples complets

Voir le fichier `example_favorites_screen_usage.dart` pour des exemples d'implémentation complète dans différents contextes :
- Écran des favoris avec actions
- Écran de recherche simple
- Lieux à proximité avec distance
- Recommandations minimalistes

## Migration

### Depuis l'ancien système
```dart
// Ancien code
_buildPlaceCard(place)

// Nouveau code
SearchStylePlaceCard(place: place)
```

### Avantages de la migration
- **Cohérence** : Design uniforme dans toute l'app
- **Réutilisabilité** : Un seul widget pour tous les cas
- **Maintenance** : Code centralisé et plus facile à maintenir
- **Fonctionnalités** : Support vidéo et actions avancées
