# Guide de Préproduction - MbokaTour

## Vue d'ensemble
Ce guide détaille les étapes pour préparer l'application MbokaTour pour la préproduction et la release.

## ✅ Changements effectués

### 1. Nom de l'application
- **Ancien**: `mboka_tour_app`
- **Nouveau**: `MbokaTour`
- **Package**: `mbokatour`

### 2. Identifiant d'application
- **Android**: `com.mbokatour.app`
- **iOS**: Configuré via Xcode

### 3. Fichiers mis à jour
- `pubspec.yaml` - Nom et description
- `android/app/src/main/AndroidManifest.xml` - Label Android
- `ios/Runner/Info.plist` - Nom d'affichage iOS
- `android/app/build.gradle` - Application ID
- Tous les imports Dart mis à jour

## 🎨 Configuration des icônes

### Prérequis
1. Créer une icône de haute qualité (1024x1024 px)
2. Format PNG avec transparence
3. Design simple et reconnaissable

### Étapes
1. Placer l'icône dans `assets/icons/app_icon.png`
2. Exécuter: `dart run flutter_launcher_icons:main`
3. Vérifier les icônes générées

### Tailles générées
- **Android**: mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi
- **iOS**: Toutes les tailles requises pour l'App Store

## 🌟 Configuration du Splash Screen

### Fichiers requis
- `assets/icons/splash_icon.png` (512x512 px recommandé)
- `flutter_native_splash.yaml` (déjà configuré)

### Génération
```bash
dart run flutter_native_splash:create
```

## 🔧 Scripts disponibles

### 1. Mise à jour des imports
```bash
./scripts/update_imports.sh
```

### 2. Build de préproduction
```bash
chmod +x scripts/build_preproduction.sh
./scripts/build_preproduction.sh
```

## 📱 Tests de préproduction

### Tests essentiels
1. **Fonctionnalités principales**
   - Navigation entre écrans
   - Affichage des lieux
   - Géolocalisation
   - Favoris et likes

2. **Performance**
   - Temps de chargement
   - Fluidité des animations
   - Consommation mémoire

3. **Compatibilité**
   - Différentes tailles d'écran
   - Versions Android/iOS
   - Connexions réseau variables

### Checklist de test
- [ ] Splash screen s'affiche correctement
- [ ] Onboarding fonctionne
- [ ] Authentification (login/register)
- [ ] Affichage des lieux
- [ ] Géolocalisation et cartes
- [ ] Navigation GPS
- [ ] Partage de lieux
- [ ] Commentaires et évaluations
- [ ] Mode hors ligne (si applicable)

## 🚀 Déploiement

### Android (Google Play)
1. Générer l'App Bundle: `flutter build appbundle --release`
2. Signer avec la clé de release
3. Tester avec Google Play Console
4. Soumettre pour review

### iOS (App Store)
1. Ouvrir le projet dans Xcode
2. Configurer les certificats
3. Build et archive
4. Soumettre via App Store Connect

## 📋 Métadonnées des stores

### Informations requises
- **Nom**: MbokaTour
- **Description courte**: Découvrez Kinshasa
- **Description longue**: Application de tourisme local pour redécouvrir Kinshasa
- **Catégorie**: Voyage & Tourisme
- **Mots-clés**: Kinshasa, tourisme, voyage, RDC, Congo

### Assets requis
- Icône de l'app (1024x1024)
- Screenshots (différentes tailles)
- Bannière/Feature graphic
- Vidéo de présentation (optionnel)

## 🔒 Sécurité et conformité

### Vérifications
- [ ] Permissions appropriées
- [ ] Politique de confidentialité
- [ ] Conformité RGPD (si applicable)
- [ ] Sécurité des données utilisateur
- [ ] Chiffrement des communications

## 📊 Analytics et monitoring

### Outils recommandés
- Firebase Analytics
- Crashlytics
- Performance Monitoring

### Métriques importantes
- Taux d'adoption
- Temps de session
- Fonctionnalités les plus utilisées
- Taux de crash

## 🔄 Processus de mise à jour

### Versioning
- Format: `MAJOR.MINOR.PATCH+BUILD`
- Exemple: `1.0.0+1`

### Types de releases
- **Patch**: Corrections de bugs
- **Minor**: Nouvelles fonctionnalités
- **Major**: Changements majeurs

## 📞 Support

### Canaux de support
- Email: <EMAIL>
- Documentation: docs/
- FAQ: À créer

### Feedback utilisateurs
- Reviews des stores
- Formulaire in-app
- Réseaux sociaux
