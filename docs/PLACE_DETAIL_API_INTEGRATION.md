# Intégration API Détails des Lieux - MbokaTour

## Vue d'ensemble
Documentation de l'intégration API pour les détails des lieux dans MbokaTour.

## Endpoints API

### 1. Récupération des détails d'un lieu
```http
GET /api/places/{id}
```

**Réponse:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Parc de la Vallée de la Nsele",
    "description": "Un magnifique parc naturel...",
    "address": "Commune de la Nsele, Kinshasa",
    "latitude": -4.4419,
    "longitude": 15.2663,
    "price": 5000,
    "currency": "CDF",
    "phone": "+243 123 456 789",
    "email": "<EMAIL>",
    "website": "https://parc-nsele.cd",
    "opening_hours": "08:00-18:00",
    "rating": 4.5,
    "views_count": 1250,
    "likes_count": 89,
    "is_liked": false,
    "is_favorite": true,
    "categories": [
      {
        "id": 1,
        "name": "Nature",
        "icon": "nature",
        "color": "#4CAF50"
      }
    ],
    "images": [
      {
        "id": 1,
        "url": "https://api.mbokatour.com/storage/places/image1.jpg",
        "type": "image",
        "order": 1
      },
      {
        "id": 2,
        "url": "https://api.mbokatour.com/storage/places/video1.mp4",
        "type": "video",
        "order": 2,
        "thumbnail": "https://api.mbokatour.com/storage/places/video1_thumb.jpg"
      }
    ],
    "comments": [
      {
        "id": 1,
        "content": "Lieu magnifique !",
        "rating": 5,
        "user": {
          "id": 1,
          "name": "John Doe"
        },
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "recommendations": [
      {
        "id": 2,
        "name": "Jardin Botanique",
        "image": "https://api.mbokatour.com/storage/places/jardin.jpg",
        "rating": 4.2
      }
    ]
  }
}
```

### 2. Incrémenter les vues
```http
POST /api/places/{id}/views
```

### 3. Commentaires
```http
# Récupérer les commentaires
GET /api/places/{id}/comments

# Ajouter un commentaire (authentification requise)
POST /api/places/{id}/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Excellent lieu !",
  "rating": 5
}

# Modifier un commentaire (auteur uniquement)
PUT /api/places/{id}/comments/{comment_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Lieu vraiment magnifique !",
  "rating": 5
}

# Supprimer un commentaire (auteur uniquement)
DELETE /api/places/{id}/comments/{comment_id}
Authorization: Bearer {token}
```

## Modèles de données

### Place Model
```dart
class Place {
  final String id;
  final String name;
  final String description;
  final String address;
  final double latitude;
  final double longitude;
  final int? price;
  final String currency;
  final String? phone;
  final String? email;
  final String? website;
  final String? openingHours;
  final double rating;
  final int viewsCount;
  int likesCount;
  bool isLiked;
  bool isFavorite;
  final List<Category> categories;
  final List<PlaceImage> images;
  final List<Comment> comments;
  final List<Place> recommendations;

  Place({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.price,
    this.currency = 'CDF',
    this.phone,
    this.email,
    this.website,
    this.openingHours,
    required this.rating,
    required this.viewsCount,
    required this.likesCount,
    required this.isLiked,
    required this.isFavorite,
    required this.categories,
    required this.images,
    required this.comments,
    required this.recommendations,
  });

  factory Place.fromJson(Map<String, dynamic> json) {
    return Place(
      id: json['id'].toString(),
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      address: json['address'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      price: json['price'],
      currency: json['currency'] ?? 'CDF',
      phone: json['phone'],
      email: json['email'],
      website: json['website'],
      openingHours: json['opening_hours'],
      rating: (json['rating'] ?? 0.0).toDouble(),
      viewsCount: json['views_count'] ?? 0,
      likesCount: json['likes_count'] ?? 0,
      isLiked: json['is_liked'] ?? false,
      isFavorite: json['is_favorite'] ?? false,
      categories: (json['categories'] as List<dynamic>?)
          ?.map((cat) => Category.fromJson(cat))
          .toList() ?? [],
      images: (json['images'] as List<dynamic>?)
          ?.map((img) => PlaceImage.fromJson(img))
          .toList() ?? [],
      comments: (json['comments'] as List<dynamic>?)
          ?.map((comment) => Comment.fromJson(comment))
          .toList() ?? [],
      recommendations: (json['recommendations'] as List<dynamic>?)
          ?.map((rec) => Place.fromJson(rec))
          .toList() ?? [],
    );
  }

  String get formattedPrice {
    if (price == null) return 'Gratuit';
    return '$price $currency';
  }

  List<PlaceImage> get imagesList {
    return images.where((img) => img.type == 'image').toList();
  }

  List<PlaceImage> get videosList {
    return images.where((img) => img.type == 'video').toList();
  }
}
```

### PlaceImage Model
```dart
class PlaceImage {
  final String id;
  final String url;
  final String type; // 'image' ou 'video'
  final int order;
  final String? thumbnail;

  PlaceImage({
    required this.id,
    required this.url,
    required this.type,
    required this.order,
    this.thumbnail,
  });

  factory PlaceImage.fromJson(Map<String, dynamic> json) {
    return PlaceImage(
      id: json['id'].toString(),
      url: json['url'] ?? '',
      type: json['type'] ?? 'image',
      order: json['order'] ?? 0,
      thumbnail: json['thumbnail'],
    );
  }

  bool get isVideo => type == 'video';
  bool get isImage => type == 'image';
}
```

## Repository Implementation

### PlaceRepository
```dart
class PlaceRepository {
  final ApiService _apiService;

  PlaceRepository(this._apiService);

  Future<Place> getPlaceById(String id) async {
    try {
      final response = await _apiService.get('/places/$id');
      
      if (response.data['success'] == true) {
        return Place.fromJson(response.data['data']);
      } else {
        throw Exception('Failed to load place details');
      }
    } catch (e) {
      throw Exception('Error fetching place details: $e');
    }
  }

  Future<void> incrementViews(String placeId) async {
    try {
      await _apiService.post('/places/$placeId/views');
    } catch (e) {
      // Les vues ne sont pas critiques, on peut ignorer les erreurs
      print('Failed to increment views: $e');
    }
  }

  Future<List<Comment>> getComments(String placeId) async {
    try {
      final response = await _apiService.get('/places/$placeId/comments');
      
      if (response.data['success'] == true) {
        return (response.data['data'] as List)
            .map((json) => Comment.fromJson(json))
            .toList();
      } else {
        throw Exception('Failed to load comments');
      }
    } catch (e) {
      throw Exception('Error fetching comments: $e');
    }
  }

  Future<Comment> addComment(String placeId, String content, int rating) async {
    try {
      final response = await _apiService.post(
        '/places/$placeId/comments',
        data: {
          'content': content,
          'rating': rating,
        },
      );
      
      if (response.data['success'] == true) {
        return Comment.fromJson(response.data['data']);
      } else {
        throw Exception('Failed to add comment');
      }
    } catch (e) {
      throw Exception('Error adding comment: $e');
    }
  }

  Future<Comment> updateComment(
    String placeId,
    String commentId,
    String content,
    int rating,
  ) async {
    try {
      final response = await _apiService.put(
        '/places/$placeId/comments/$commentId',
        data: {
          'content': content,
          'rating': rating,
        },
      );
      
      if (response.data['success'] == true) {
        return Comment.fromJson(response.data['data']);
      } else {
        throw Exception('Failed to update comment');
      }
    } catch (e) {
      throw Exception('Error updating comment: $e');
    }
  }

  Future<void> deleteComment(String placeId, String commentId) async {
    try {
      await _apiService.delete('/places/$placeId/comments/$commentId');
    } catch (e) {
      throw Exception('Error deleting comment: $e');
    }
  }
}
```

## Controller Implementation

### PlaceController
```dart
class PlaceController {
  final PlaceRepository _repository;

  PlaceController(this._repository);

  Future<Place> loadPlaceDetails(String placeId) async {
    try {
      final place = await _repository.getPlaceById(placeId);
      
      // Incrémenter les vues de manière asynchrone
      _repository.incrementViews(placeId).catchError((e) {
        print('Failed to increment views: $e');
      });
      
      return place;
    } catch (e) {
      throw Exception('Failed to load place details: $e');
    }
  }

  Future<List<Comment>> loadComments(String placeId) async {
    try {
      return await _repository.getComments(placeId);
    } catch (e) {
      throw Exception('Failed to load comments: $e');
    }
  }

  Future<Comment> submitComment(
    String placeId,
    String content,
    int rating,
  ) async {
    if (content.trim().length < 3) {
      throw Exception('Le commentaire doit contenir au moins 3 caractères');
    }
    
    if (content.length > 1000) {
      throw Exception('Le commentaire ne peut pas dépasser 1000 caractères');
    }
    
    if (rating < 1 || rating > 5) {
      throw Exception('La note doit être entre 1 et 5 étoiles');
    }

    try {
      return await _repository.addComment(placeId, content, rating);
    } catch (e) {
      throw Exception('Failed to submit comment: $e');
    }
  }

  Future<Comment> editComment(
    String placeId,
    String commentId,
    String content,
    int rating,
  ) async {
    if (content.trim().length < 3) {
      throw Exception('Le commentaire doit contenir au moins 3 caractères');
    }
    
    if (content.length > 1000) {
      throw Exception('Le commentaire ne peut pas dépasser 1000 caractères');
    }

    try {
      return await _repository.updateComment(placeId, commentId, content, rating);
    } catch (e) {
      throw Exception('Failed to edit comment: $e');
    }
  }

  Future<void> removeComment(String placeId, String commentId) async {
    try {
      await _repository.deleteComment(placeId, commentId);
    } catch (e) {
      throw Exception('Failed to delete comment: $e');
    }
  }
}
```

## Gestion des erreurs

### Types d'erreurs
```dart
enum PlaceDetailError {
  networkError,
  notFound,
  unauthorized,
  validationError,
  serverError,
}

class PlaceDetailException implements Exception {
  final PlaceDetailError type;
  final String message;
  final dynamic originalError;

  PlaceDetailException({
    required this.type,
    required this.message,
    this.originalError,
  });

  @override
  String toString() => message;
}
```

### Gestion dans le Repository
```dart
Future<Place> getPlaceById(String id) async {
  try {
    final response = await _apiService.get('/places/$id');
    return Place.fromJson(response.data['data']);
  } on DioException catch (e) {
    switch (e.response?.statusCode) {
      case 404:
        throw PlaceDetailException(
          type: PlaceDetailError.notFound,
          message: 'Ce lieu n\'existe pas ou a été supprimé',
          originalError: e,
        );
      case 401:
        throw PlaceDetailException(
          type: PlaceDetailError.unauthorized,
          message: 'Vous devez être connecté pour accéder à ce lieu',
          originalError: e,
        );
      case 500:
        throw PlaceDetailException(
          type: PlaceDetailError.serverError,
          message: 'Erreur du serveur, veuillez réessayer plus tard',
          originalError: e,
        );
      default:
        throw PlaceDetailException(
          type: PlaceDetailError.networkError,
          message: 'Erreur de connexion, vérifiez votre internet',
          originalError: e,
        );
    }
  } catch (e) {
    throw PlaceDetailException(
      type: PlaceDetailError.networkError,
      message: 'Une erreur inattendue s\'est produite',
      originalError: e,
    );
  }
}
```

## Optimisations

### 1. Cache des détails
```dart
class PlaceDetailCache {
  static final Map<String, Place> _cache = {};
  static const Duration cacheTimeout = Duration(minutes: 10);
  static final Map<String, DateTime> _timestamps = {};

  static Place? get(String placeId) {
    final timestamp = _timestamps[placeId];
    if (timestamp != null) {
      if (DateTime.now().difference(timestamp) < cacheTimeout) {
        return _cache[placeId];
      } else {
        // Cache expiré
        _cache.remove(placeId);
        _timestamps.remove(placeId);
      }
    }
    return null;
  }

  static void set(String placeId, Place place) {
    _cache[placeId] = place;
    _timestamps[placeId] = DateTime.now();
  }

  static void clear() {
    _cache.clear();
    _timestamps.clear();
  }
}
```

### 2. Chargement progressif
```dart
Future<Place> loadPlaceWithProgressiveLoading(String placeId) async {
  // 1. Charger les données de base depuis le cache ou l'API
  Place place = await _loadBasicPlaceData(placeId);
  
  // 2. Charger les commentaires en arrière-plan
  _loadCommentsInBackground(placeId).then((comments) {
    place.comments.clear();
    place.comments.addAll(comments);
    notifyListeners(); // Si utilisation de Provider
  });
  
  // 3. Charger les recommandations en arrière-plan
  _loadRecommendationsInBackground(placeId).then((recommendations) {
    place.recommendations.clear();
    place.recommendations.addAll(recommendations);
    notifyListeners();
  });
  
  return place;
}
```

### 3. Préchargement des images
```dart
Future<void> preloadImages(List<PlaceImage> images) async {
  for (final image in images) {
    if (image.isImage) {
      try {
        await precacheImage(
          CachedNetworkImageProvider(image.url),
          context,
        );
      } catch (e) {
        print('Failed to preload image: ${image.url}');
      }
    }
  }
}
```

## Tests

### Tests unitaires
```dart
group('PlaceRepository Tests', () {
  late PlaceRepository repository;
  late MockApiService mockApiService;

  setUp(() {
    mockApiService = MockApiService();
    repository = PlaceRepository(mockApiService);
  });

  test('should return place details when API call is successful', () async {
    // Arrange
    final mockResponse = {
      'success': true,
      'data': {
        'id': 1,
        'name': 'Test Place',
        'description': 'Test Description',
        // ... autres champs
      }
    };
    
    when(mockApiService.get('/places/1'))
        .thenAnswer((_) async => Response(
          data: mockResponse,
          statusCode: 200,
          requestOptions: RequestOptions(path: '/places/1'),
        ));

    // Act
    final result = await repository.getPlaceById('1');

    // Assert
    expect(result.id, '1');
    expect(result.name, 'Test Place');
    verify(mockApiService.get('/places/1')).called(1);
  });

  test('should throw PlaceDetailException when place not found', () async {
    // Arrange
    when(mockApiService.get('/places/999'))
        .thenThrow(DioException(
          response: Response(
            statusCode: 404,
            requestOptions: RequestOptions(path: '/places/999'),
          ),
          requestOptions: RequestOptions(path: '/places/999'),
        ));

    // Act & Assert
    expect(
      () => repository.getPlaceById('999'),
      throwsA(isA<PlaceDetailException>()
          .having((e) => e.type, 'type', PlaceDetailError.notFound)),
    );
  });
});
```
