# Intégration API - MbokaTour

## Vue d'ensemble
Documentation complète de l'intégration avec l'API MbokaTour.

## Configuration de base

### URLs d'API
```dart
// Production
const String prodUrl = 'https://mbokatour.com/api';

// Développement
const String devUrl = 'http://localhost:8000/api';
// Émulateur Android
const String emulatorUrl = 'http://********:8000/api';
```

### Client HTTP (Dio)
```dart
class ApiService {
  static final Dio _dio = Dio();
  
  static void initialize() {
    _dio.options.baseUrl = AppConfig.baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
    
    // Intercepteur pour l'authentification
    _dio.interceptors.add(AuthInterceptor());
  }
}
```

## Endpoints principaux

### 1. Authentification

#### Connexion
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Réponse:**
```json
{
  "success": true,
  "token": "bearer_token",
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

#### Inscription
```http
POST /api/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password",
  "password_confirmation": "password"
}
```

### 2. Lieux

#### Liste des lieux
```http
GET /api/places
```

**Paramètres optionnels:**
- `category_id`: Filtrer par catégorie
- `latitude` & `longitude`: Lieux proches
- `radius`: Rayon de recherche (km)
- `limit`: Nombre de résultats

#### Détails d'un lieu
```http
GET /api/places/{id}
```

#### Recherche de lieux
```http
GET /api/places/search?q=terme_recherche
```

### 3. Catégories

#### Liste des catégories
```http
GET /api/categories
```

**Réponse:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Nature",
      "icon": "nature_icon",
      "color": "#4CAF50"
    }
  ]
}
```

### 4. Favoris (Authentification requise)

#### Ajouter aux favoris
```http
POST /api/user/favorites/places
Authorization: Bearer {token}
Content-Type: application/json

{
  "place_id": 1
}
```

#### Supprimer des favoris
```http
DELETE /api/user/favorites/places/{place_id}
Authorization: Bearer {token}
```

#### Liste des favoris
```http
GET /api/user/favorites/places
Authorization: Bearer {token}
```

### 5. Likes (Authentification requise)

#### Toggle like
```http
POST /api/places/likes/toggle
Authorization: Bearer {token}
Content-Type: application/json

{
  "place_id": 1
}
```

### 6. Commentaires

#### Récupérer les commentaires
```http
GET /api/places/{id}/comments
```

#### Ajouter un commentaire (Authentification requise)
```http
POST /api/places/{id}/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Excellent lieu !",
  "rating": 5
}
```

## Gestion des erreurs

### Codes de statut
- **200**: Succès
- **201**: Créé avec succès
- **400**: Requête invalide
- **401**: Non authentifié
- **403**: Non autorisé
- **404**: Ressource non trouvée
- **422**: Erreur de validation
- **500**: Erreur serveur

### Gestion dans Flutter
```dart
try {
  final response = await dio.get('/api/places');
  return response.data;
} on DioException catch (e) {
  switch (e.response?.statusCode) {
    case 401:
      // Rediriger vers la connexion
      AuthService.instance.logout();
      break;
    case 404:
      throw Exception('Ressource non trouvée');
    case 500:
      throw Exception('Erreur serveur');
    default:
      throw Exception('Erreur réseau: ${e.message}');
  }
}
```

## Authentification automatique

### Intercepteur Dio
```dart
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = AuthService.instance.currentToken;
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      AuthService.instance.logout();
      // Rediriger vers la connexion
    }
    handler.next(err);
  }
}
```

## Optimisations

### 1. Cache des réponses
```dart
// Cache simple en mémoire
class ApiCache {
  static final Map<String, dynamic> _cache = {};
  static const Duration cacheTimeout = Duration(minutes: 5);
  
  static void set(String key, dynamic data) {
    _cache[key] = {
      'data': data,
      'timestamp': DateTime.now(),
    };
  }
  
  static dynamic get(String key) {
    final cached = _cache[key];
    if (cached != null) {
      final timestamp = cached['timestamp'] as DateTime;
      if (DateTime.now().difference(timestamp) < cacheTimeout) {
        return cached['data'];
      }
    }
    return null;
  }
}
```

### 2. Pagination
```dart
class PaginatedResponse<T> {
  final List<T> data;
  final int currentPage;
  final int totalPages;
  final bool hasNextPage;
  
  PaginatedResponse({
    required this.data,
    required this.currentPage,
    required this.totalPages,
    required this.hasNextPage,
  });
}
```

### 3. Retry automatique
```dart
dio.interceptors.add(RetryInterceptor(
  dio: dio,
  logPrint: print,
  retries: 3,
  retryDelays: const [
    Duration(seconds: 1),
    Duration(seconds: 2),
    Duration(seconds: 3),
  ],
));
```

## Tests d'API

### 1. Test de connectivité
```dart
Future<bool> testApiConnection() async {
  try {
    final response = await dio.get('/health');
    return response.statusCode == 200;
  } catch (e) {
    return false;
  }
}
```

### 2. Test des endpoints
```dart
void main() async {
  group('API Tests', () {
    test('Should fetch places', () async {
      final places = await PlaceRepository().getPlaces();
      expect(places, isNotEmpty);
    });
    
    test('Should authenticate user', () async {
      final result = await AuthRepository().login(
        '<EMAIL>',
        'password',
      );
      expect(result.token, isNotNull);
    });
  });
}
```

## Monitoring et logs

### 1. Logs des requêtes
```dart
dio.interceptors.add(LogInterceptor(
  requestBody: true,
  responseBody: true,
  logPrint: (object) {
    if (kDebugMode) {
      print(object);
    }
  },
));
```

### 2. Métriques de performance
```dart
class ApiMetrics {
  static final Map<String, Duration> _responseTimes = {};
  
  static void recordResponseTime(String endpoint, Duration time) {
    _responseTimes[endpoint] = time;
  }
  
  static Duration? getAverageResponseTime(String endpoint) {
    return _responseTimes[endpoint];
  }
}
```

## Sécurité

### 1. Validation des certificats
```dart
dio.options.validateStatus = (status) {
  return status != null && status < 500;
};
```

### 2. Timeout appropriés
```dart
dio.options.connectTimeout = const Duration(seconds: 10);
dio.options.receiveTimeout = const Duration(seconds: 30);
dio.options.sendTimeout = const Duration(seconds: 30);
```

### 3. Gestion des tokens
- Stockage sécurisé avec SharedPreferences
- Refresh automatique des tokens expirés
- Nettoyage des tokens lors de la déconnexion

## Ressources utiles
- [Documentation Dio](https://pub.dev/packages/dio)
- [HTTP Status Codes](https://httpstatuses.com/)
- [REST API Best Practices](https://restfulapi.net/)
