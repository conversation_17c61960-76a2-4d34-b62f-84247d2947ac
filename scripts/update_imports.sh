#!/bin/bash

# Script pour mettre à jour tous les imports du package mboka_tour_app vers mbokatour

echo "🔄 Mise à jour des imports du package..."

# Fonction pour mettre à jour les imports dans un fichier
update_imports() {
    local file="$1"
    if [[ -f "$file" ]]; then
        echo "Mise à jour: $file"
        # Remplacer mboka_tour_app par mbokatour dans les imports
        sed -i '' 's/package:mboka_tour_app\//package:mbokatour\//g' "$file"
    fi
}

# Mettre à jour tous les fichiers Dart
find lib -name "*.dart" -type f | while read -r file; do
    update_imports "$file"
done

# Mettre à jour le fichier MainActivity.kt
if [[ -f "android/app/src/main/kotlin/com/example/mboka_tour_app/MainActivity.kt" ]]; then
    echo "Mise à jour: MainActivity.kt"
    # Mettre à jour le package name dans MainActivity.kt
    sed -i '' 's/package com\.example\.mboka_tour_app/package com.mbokatour.app/g' "android/app/src/main/kotlin/com/example/mboka_tour_app/MainActivity.kt"
    
    # Déplacer le fichier vers le nouveau répertoire
    mkdir -p "android/app/src/main/kotlin/com/mbokatour/app"
    mv "android/app/src/main/kotlin/com/example/mboka_tour_app/MainActivity.kt" "android/app/src/main/kotlin/com/mbokatour/app/"
    
    # Supprimer l'ancien répertoire s'il est vide
    rmdir "android/app/src/main/kotlin/com/example/mboka_tour_app" 2>/dev/null || true
    rmdir "android/app/src/main/kotlin/com/example" 2>/dev/null || true
fi

echo "✅ Mise à jour des imports terminée!"
echo ""
echo "📋 Prochaines étapes:"
echo "1. Exécuter: flutter clean"
echo "2. Exécuter: flutter pub get"
echo "3. Tester la compilation: flutter build apk --debug"
