#!/bin/bash

echo "🚀 Build MbokaTour avec Java 17"
echo "================================"

# Vérifier si Java 17 est installé
if ! command -v java &> /dev/null; then
    echo "❌ Java n'est pas installé"
    exit 1
fi

# Essayer de trouver Java 17
JAVA17_HOME=""

# Chemins possibles pour Java 17 sur macOS
POSSIBLE_PATHS=(
    "/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"
    "/Library/Java/JavaVirtualMachines/temurin-17.jdk/Contents/Home"
    "/Library/Java/JavaVirtualMachines/openjdk-17.jdk/Contents/Home"
    "/opt/homebrew/opt/openjdk@17"
    "/usr/local/opt/openjdk@17"
)

for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -d "$path" ]; then
        JAVA17_HOME="$path"
        break
    fi
done

if [ -z "$JAVA17_HOME" ]; then
    echo "⚠️  Java 17 non trouvé. Installation avec Homebrew..."
    if command -v brew &> /dev/null; then
        brew install openjdk@17
        JAVA17_HOME="/opt/homebrew/opt/openjdk@17"
    else
        echo "❌ Homebrew non installé. Veuillez installer Java 17 manuellement."
        echo "   Téléchargez depuis: https://adoptium.net/temurin/releases/"
        exit 1
    fi
fi

echo "✅ Java 17 trouvé: $JAVA17_HOME"

# Exporter JAVA_HOME pour ce build
export JAVA_HOME="$JAVA17_HOME"
export PATH="$JAVA_HOME/bin:$PATH"

echo "🔧 Configuration Java:"
java -version

echo ""
echo "🧹 Nettoyage du projet..."
flutter clean

echo "📦 Récupération des dépendances..."
flutter pub get

echo "🎨 Génération des icônes..."
flutter pub run flutter_launcher_icons:main

echo "🌟 Génération du splash screen..."
flutter pub run flutter_native_splash:create

echo "🤖 Build Android APK (Release) avec Java 17..."
flutter build apk --release

if [ $? -eq 0 ]; then
    echo "✅ APK généré avec succès!"
    
    echo "📱 Build Android App Bundle..."
    flutter build appbundle --release
    
    if [ $? -eq 0 ]; then
        echo "✅ App Bundle généré avec succès!"
    else
        echo "⚠️  Échec du build App Bundle, mais APK disponible"
    fi
else
    echo "❌ Échec du build APK"
    exit 1
fi

echo ""
echo "✅ Build terminé!"
echo ""
echo "📁 Fichiers générés:"
if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
    echo "   ✅ Android APK: build/app/outputs/flutter-apk/app-release.apk"
    ls -lh build/app/outputs/flutter-apk/app-release.apk
fi

if [ -f "build/app/outputs/bundle/release/app-release.aab" ]; then
    echo "   ✅ Android Bundle: build/app/outputs/bundle/release/app-release.aab"
    ls -lh build/app/outputs/bundle/release/app-release.aab
fi

echo ""
echo "📋 Prochaines étapes:"
echo "1. Tester l'APK sur un appareil Android"
echo "2. Vérifier que toutes les fonctionnalités marchent"
echo "3. Préparer les métadonnées pour les stores"
echo ""
echo "ℹ️  Informations de version:"
grep "version:" pubspec.yaml
