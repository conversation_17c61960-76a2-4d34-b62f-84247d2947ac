#!/bin/bash

echo "🚀 Build MbokaTour - Version sans vidéo (temporaire)"
echo "=================================================="

# Sauvegarder les fichiers originaux
echo "💾 Sauvegarde des fichiers originaux..."
cp pubspec.yaml pubspec.yaml.backup
cp lib/widgets/search_style_place_card.dart lib/widgets/search_style_place_card.dart.backup
cp lib/views/home/<USER>/views/home/<USER>
cp lib/views/detail/place_detail_screen.dart lib/views/detail/place_detail_screen.dart.backup
cp lib/services/video_cache_service.dart lib/services/video_cache_service.dart.backup

# Créer une version temporaire sans video_player
echo "🔧 Création de la version temporaire..."

# Supprimer video_player du pubspec.yaml
sed -i '' '/chewie:/d' pubspec.yaml
sed -i '' '/video_player:/d' pubspec.yaml

# Nettoyer le projet
echo "🧹 Nettoyage du projet..."
flutter clean

# Récupérer les dépendances
echo "📦 Récupération des dépendances..."
flutter pub get

# Générer les icônes
echo "🎨 Génération des icônes..."
flutter pub run flutter_launcher_icons:main

# Générer le splash screen
echo "🌟 Génération du splash screen..."
flutter pub run flutter_native_splash:create

# Build Android APK
echo "🤖 Build Android APK (Release)..."
flutter build apk --release

# Build Android App Bundle
echo "📱 Build Android App Bundle..."
flutter build appbundle --release

# Restaurer les fichiers originaux
echo "🔄 Restauration des fichiers originaux..."
mv pubspec.yaml.backup pubspec.yaml
mv lib/widgets/search_style_place_card.dart.backup lib/widgets/search_style_place_card.dart
mv lib/views/home/<USER>/views/home/<USER>
mv lib/views/detail/place_detail_screen.dart.backup lib/views/detail/place_detail_screen.dart
mv lib/services/video_cache_service.dart.backup lib/services/video_cache_service.dart

# Restaurer les dépendances
echo "📦 Restauration des dépendances..."
flutter pub get

echo ""
echo "✅ Build terminé!"
echo ""
echo "📁 Fichiers générés:"
echo "   - Android APK: build/app/outputs/flutter-apk/app-release.apk"
echo "   - Android Bundle: build/app/outputs/bundle/release/app-release.aab"
echo ""
echo "⚠️  Note: Cette version ne contient pas les fonctionnalités vidéo"
echo "   Les vidéos seront affichées comme des images statiques"
echo ""
echo "📋 Prochaines étapes:"
echo "1. Tester l'APK sur un appareil Android"
echo "2. Vérifier que toutes les fonctionnalités (sauf vidéo) marchent"
echo "3. Résoudre le problème video_player pour la version finale"
echo ""
echo "ℹ️  Informations de version:"
grep "version:" pubspec.yaml
