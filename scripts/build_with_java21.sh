#!/bin/bash

echo "🚀 Build MbokaTour avec Java 21"
echo "================================"

# Fonction pour trouver Java 21
find_java21() {
    # Chemins possibles pour Java 21 sur macOS
    local possible_paths=(
        "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home"
        "/Library/Java/JavaVirtualMachines/temurin-21.jdk/Contents/Home"
        "/Library/Java/JavaVirtualMachines/openjdk-21.jdk/Contents/Home"
        "/opt/homebrew/opt/openjdk@21"
        "/usr/local/opt/openjdk@21"
        "/Library/Java/JavaVirtualMachines/adoptopenjdk-21.jdk/Contents/Home"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -d "$path" ]; then
            echo "$path"
            return 0
        fi
    done
    
    return 1
}

# Chercher Java 21
JAVA21_HOME=$(find_java21)

if [ -z "$JAVA21_HOME" ]; then
    echo "❌ Java 21 non trouvé. Installation automatique..."
    
    # Vérifier si Homebrew est installé
    if command -v brew &> /dev/null; then
        echo "📦 Installation de Java 21 avec Homebrew..."
        brew install openjdk@21
        
        # Créer le lien symbolique
        sudo ln -sfn /opt/homebrew/opt/openjdk@21/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-21.jdk
        
        JAVA21_HOME="/opt/homebrew/opt/openjdk@21"
    else
        echo "❌ Homebrew non installé. Veuillez installer Java 21 manuellement."
        echo "   Téléchargez depuis: https://adoptium.net/temurin/releases/?version=21"
        echo "   Ou installez Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
fi

echo "✅ Java 21 trouvé: $JAVA21_HOME"

# Configurer l'environnement Java pour ce build
export JAVA_HOME="$JAVA21_HOME"
export PATH="$JAVA_HOME/bin:$PATH"

# Vérifier la version
echo "🔧 Configuration Java:"
java -version
echo ""

# Configurer Gradle pour utiliser Java 21
export GRADLE_OPTS="-Dorg.gradle.java.home=$JAVA_HOME"

echo "🧹 Nettoyage du projet..."
flutter clean

echo "📦 Récupération des dépendances..."
flutter pub get

echo "🎨 Génération des icônes..."
flutter pub run flutter_launcher_icons:main

echo "🌟 Génération du splash screen..."
flutter pub run flutter_native_splash:create

echo "🤖 Build Android APK (Release) avec Java 21..."
flutter build apk --release

if [ $? -eq 0 ]; then
    echo "✅ APK généré avec succès!"
    
    echo "📱 Build Android App Bundle..."
    flutter build appbundle --release
    
    if [ $? -eq 0 ]; then
        echo "✅ App Bundle généré avec succès!"
    else
        echo "⚠️  Échec du build App Bundle, mais APK disponible"
    fi
else
    echo "❌ Échec du build APK"
    echo ""
    echo "🔧 Solutions alternatives:"
    echo "1. Vérifiez que Java 21 est correctement installé"
    echo "2. Redémarrez votre terminal"
    echo "3. Essayez: export JAVA_HOME=$JAVA21_HOME"
    exit 1
fi

echo ""
echo "✅ Build terminé avec Java 21!"
echo ""
echo "📁 Fichiers générés:"
if [ -f "build/app/outputs/flutter-apk/app-release.apk" ]; then
    echo "   ✅ Android APK: build/app/outputs/flutter-apk/app-release.apk"
    ls -lh build/app/outputs/flutter-apk/app-release.apk
fi

if [ -f "build/app/outputs/bundle/release/app-release.aab" ]; then
    echo "   ✅ Android Bundle: build/app/outputs/bundle/release/app-release.aab"
    ls -lh build/app/outputs/bundle/release/app-release.aab
fi

echo ""
echo "📋 Prochaines étapes:"
echo "1. Tester l'APK sur un appareil Android"
echo "2. Vérifier que toutes les fonctionnalités marchent"
echo "3. Préparer les métadonnées pour les stores"
echo ""
echo "ℹ️  Informations de version:"
grep "version:" pubspec.yaml
