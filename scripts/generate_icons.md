# Génération des icônes MbokaTour

## Prérequis
1. Avoir une icône source de haute qualité (1024x1024 px minimum)
2. Installer le package `flutter_launcher_icons`

## Installation du générateur d'icônes

```bash
flutter pub add --dev flutter_launcher_icons
```

## Configuration

Créez un fichier `flutter_launcher_icons.yaml` à la racine du projet :

```yaml
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"
```

## Génération des icônes

```bash
# Générer toutes les icônes
flutter pub get
flutter pub run flutter_launcher_icons:main

# Ou avec dart
dart run flutter_launcher_icons:main
```

## Tailles d'icônes requises

### Android
- 48x48 (mdpi)
- 72x72 (hdpi)
- 96x96 (xhdpi)
- 144x144 (xxhdpi)
- 192x192 (xxxhdpi)

### iOS
- 20x20, 29x29, 40x40, 60x60, 76x76, 83.5x83.5
- Versions @1x, @2x, @3x
- 1024x1024 pour l'App Store

## Recommandations pour l'icône MbokaTour

1. **Design simple et reconnaissable**
   - Logo MbokaTour centré
   - Couleurs de la marque
   - Contraste élevé

2. **Format**
   - PNG avec transparence
   - Résolution 1024x1024 minimum
   - Pas de texte trop petit

3. **Test**
   - Tester sur différentes tailles
   - Vérifier la lisibilité
   - Tester sur fond clair et sombre

## Après génération

1. Vérifier que les icônes sont générées dans :
   - `android/app/src/main/res/mipmap-*/`
   - `ios/Runner/Assets.xcassets/AppIcon.appiconset/`

2. Nettoyer et rebuilder :
   ```bash
   flutter clean
   flutter pub get
   flutter build apk --release
   ```

## Icône de lancement (Splash Screen)

Pour personnaliser l'écran de lancement, utilisez `flutter_native_splash` :

```bash
flutter pub add --dev flutter_native_splash
```

Configuration dans `pubspec.yaml` :
```yaml
flutter_native_splash:
  color: "#ffffff"
  image: assets/icons/splash_icon.png
  android_12:
    image: assets/icons/splash_icon.png
    color: "#ffffff"
```

Génération :
```bash
dart run flutter_native_splash:create
```
