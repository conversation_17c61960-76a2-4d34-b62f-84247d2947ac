#!/bin/bash

# Script de build pour la préproduction MbokaTour

echo "🚀 Build MbokaTour - Préproduction"
echo "=================================="

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Erreur: pubspec.yaml non trouvé. Exécutez ce script depuis la racine du projet."
    exit 1
fi

# Nettoyer le projet
echo "🧹 Nettoyage du projet..."
flutter clean

# Récupérer les dépendances
echo "📦 Récupération des dépendances..."
flutter pub get

# Générer les icônes (si le fichier app_icon.png existe)
if [ -f "assets/icons/app_icon.png" ]; then
    echo "🎨 Génération des icônes..."
    dart run flutter_launcher_icons:main
else
    echo "⚠️  Icône app_icon.png non trouvée dans assets/icons/"
    echo "   Veuillez ajouter votre icône avant de continuer."
    echo "   Taille recommandée: 1024x1024 px"
fi

# Générer le splash screen (si configuré)
if [ -f "flutter_native_splash.yaml" ]; then
    echo "🌟 Génération du splash screen..."
    dart run flutter_native_splash:create
fi

# Build Android APK (Release)
echo "🤖 Build Android APK (Release)..."
flutter build apk --release

# Build Android App Bundle (pour Play Store)
echo "📱 Build Android App Bundle..."
flutter build appbundle --release

# Build iOS (si sur macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Build iOS..."
    flutter build ios --release --no-codesign
else
    echo "⏭️  Build iOS ignoré (pas sur macOS)"
fi

echo ""
echo "✅ Build terminé!"
echo ""
echo "📁 Fichiers générés:"
echo "   - Android APK: build/app/outputs/flutter-apk/app-release.apk"
echo "   - Android Bundle: build/app/outputs/bundle/release/app-release.aab"
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "   - iOS: build/ios/iphoneos/Runner.app"
fi

echo ""
echo "📋 Prochaines étapes:"
echo "1. Tester l'APK sur un appareil Android"
echo "2. Vérifier que toutes les fonctionnalités marchent"
echo "3. Préparer les métadonnées pour les stores"
echo "4. Soumettre aux stores (Google Play, App Store)"

# Afficher les informations de version
echo ""
echo "ℹ️  Informations de version:"
grep "version:" pubspec.yaml
