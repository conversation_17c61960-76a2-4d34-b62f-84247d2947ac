# Architecture Flutter - MbokaTour

## Vue d'ensemble
Ce document décrit l'architecture de l'application Flutter MbokaTour.

## Structure du projet

```
lib/
├── config/           # Configuration de l'application
├── controllers/      # Logique métier
├── models/          # Modèles de données
├── providers/       # Gestion d'état avec Provider
├── repositories/    # Couche d'accès aux données
├── services/        # Services transversaux
├── views/           # Interface utilisateur
├── widgets/         # Composants réutilisables
└── main.dart        # Point d'entrée
```

## Couches de l'architecture

### 1. Configuration (`config/`)
- **app_config.dart** : Configuration globale (couleurs, API URLs)
- **app_theme.dart** : Thème et styles de l'application

### 2. Modèles (`models/`)
- **place.dart** : Modèle des lieux
- **category.dart** : Modèle des catégories
- **user.dart** : Modèle utilisateur
- **comment.dart** : Modèle des commentaires
- **place_image.dart** : Modèle des images/vidéos

### 3. Services (`services/`)
- **api_service.dart** : Client HTTP avec Dio
- **auth_service.dart** : Gestion de l'authentification
- **location_service.dart** : Services de géolocalisation
- **deep_link_service.dart** : Gestion des liens profonds
- **video_cache_service.dart** : Cache des vidéos

### 4. Repositories (`repositories/`)
Couche d'abstraction pour l'accès aux données :
- **place_repository.dart** : CRUD des lieux
- **auth_repository.dart** : Authentification
- **category_repository.dart** : Gestion des catégories
- **comment_repository.dart** : Gestion des commentaires

### 5. Controllers (`controllers/`)
Logique métier et gestion d'état :
- **place_controller.dart** : Logique des lieux
- **auth_controller.dart** : Logique d'authentification
- **category_controller.dart** : Logique des catégories
- **comment_controller.dart** : Logique des commentaires

### 6. Providers (`providers/`)
Gestion d'état avec Provider pattern :
- **category_provider.dart** : État des catégories

### 7. Views (`views/`)
Interface utilisateur organisée par fonctionnalité :

```
views/
├── splash/          # Écran de démarrage
├── onboarding/      # Introduction de l'app
├── auth/            # Authentification
├── home/            # Écrans principaux
├── detail/          # Détails des lieux
├── map/             # Cartes et navigation
└── navigation/      # Navigation GPS
```

### 8. Widgets (`widgets/`)
Composants réutilisables :
- **place_card.dart** : Carte de lieu
- **glass_container.dart** : Effet glassmorphism
- **auth_guard.dart** : Protection d'authentification
- **auth_required_*.dart** : Widgets d'authentification

## Patterns utilisés

### 1. Repository Pattern
Abstraction de la couche de données :
```dart
abstract class PlaceRepository {
  Future<List<Place>> getPlaces();
  Future<Place> getPlaceById(String id);
}
```

### 2. Provider Pattern
Gestion d'état réactive :
```dart
ChangeNotifierProvider(
  create: (_) => CategoryProvider(),
  child: MyApp(),
)
```

### 3. Service Locator
Services singleton :
```dart
class AuthService {
  static final AuthService _instance = AuthService._internal();
  static AuthService get instance => _instance;
}
```

### 4. Factory Pattern
Création d'objets à partir de JSON :
```dart
factory Place.fromJson(Map<String, dynamic> json) {
  return Place(
    id: json['id'],
    name: json['name'],
    // ...
  );
}
```

## Gestion d'état

### Provider
Utilisé pour la gestion d'état globale :
- État des catégories
- Données utilisateur
- Configuration de l'application

### StatefulWidget
Pour l'état local des écrans :
- Formulaires
- Animations
- États temporaires

## Navigation

### Routes nommées
```dart
MaterialApp(
  routes: {
    '/': (context) => SplashScreen(),
    '/onboarding': (context) => OnboardingScreen(),
    '/home': (context) => HomeScreen(),
    '/login': (context) => LoginScreen(),
  },
)
```

### Navigation programmatique
```dart
Navigator.pushNamed(context, '/login');
Navigator.pushReplacement(context, route);
```

## Gestion des erreurs

### Try-Catch global
```dart
try {
  final result = await repository.getData();
  return result;
} catch (e) {
  throw Exception('Error: $e');
}
```

### Gestion des erreurs réseau
```dart
dio.interceptors.add(InterceptorsWrapper(
  onError: (error, handler) {
    // Gestion centralisée des erreurs
  },
));
```

## Performance

### Optimisations vidéo
- Cache intelligent des contrôleurs vidéo
- Disposal automatique des ressources
- Lecture conditionnelle (visible uniquement)

### Optimisations images
- Mise en cache avec CachedNetworkImage
- Placeholder et gestion d'erreur
- Compression automatique

### Optimisations cartes
- Clustering des marqueurs
- Chargement paresseux des données
- Limitation du nombre de lieux affichés

## Tests

### Structure des tests
```
test/
├── models/          # Tests des modèles
├── widgets/         # Tests des widgets
├── config_test.dart # Tests de configuration
└── widget_test.dart # Tests d'intégration
```

### Types de tests
- **Unit tests** : Logique métier
- **Widget tests** : Interface utilisateur
- **Integration tests** : Flux complets

## Bonnes pratiques

### 1. Séparation des responsabilités
Chaque couche a une responsabilité claire et définie.

### 2. Injection de dépendances
Les services sont injectés via constructeurs ou singletons.

### 3. Gestion des ressources
Disposal automatique des contrôleurs et streams.

### 4. Code réutilisable
Widgets et services modulaires et réutilisables.

### 5. Configuration centralisée
Toute la configuration dans `app_config.dart`.

## Déploiement

### Environnements
- **Development** : localhost:8000
- **Production** : mbokatour.com

### Configuration
```dart
static const bool isProd = bool.fromEnvironment('dart.vm.product');
static String get baseUrl => isProd 
  ? 'https://mbokatour.com/api' 
  : 'http://localhost:8000/api';
```
